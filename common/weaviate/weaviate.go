package weaviate

import (
	"bytes"
	"encoding/json"

	"github.com/precize/logger"
	"github.com/precize/transport"
)

func Insert(className string, objects any) error {

	reqBody := map[string]any{
		"className": className,
		"objects":   objects,
	}

	body, err := json.Marshal(reqBody)
	if err != nil {
		logger.Print(logger.ERROR, "Marshal failed", err)
		return err
	}

	resp, err := transport.SendRequestToProviderServer(
		"POST",
		"/provider/weaviate/data/insert",
		nil,
		bytes.NewBuffer(body),
	)
	if err != nil {
		return err
	}

	logger.Print(logger.INFO, "Weaviate insert successful", string(resp))
	return nil
}

func InsertWithID(className string, objects any) error {
	reqBody := map[string]any{
		"className": className,
		"objects":   objects,
	}

	body, err := json.Marshal(reqBody)
	if err != nil {
		logger.Print(logger.ERROR, "Marshal failed", err)
		return err
	}

	resp, err := transport.SendRequestToProviderServer(
		"POST",
		"/provider/weaviate/data/insertById",
		nil,
		bytes.NewBuffer(body),
	)
	if err != nil {
		return err
	}

	logger.Print(logger.INFO, "Weaviate insert successful", string(resp))
	return nil
}

func SearchByID(className string, id string) ([]byte, error) {
	reqBody := map[string]any{
		"className": className,
		"id":        id,
	}

	body, err := json.Marshal(reqBody)
	if err != nil {
		logger.Print(logger.ERROR, "Marshal failed", err)
		return nil, err
	}

	resp, err := transport.SendRequestToProviderServer(
		"POST",
		"/provider/weaviate/search/id",
		nil,
		bytes.NewBuffer(body),
	)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func SearchByQuery(className, filters string, fields []string, limit int) ([]byte, error) {
	reqBody := QuerySearchReq{
		ClassName: className,
		Filters:   filters,
		Fields:    fields,
		Limit:     limit,
	}

	body, err := json.Marshal(reqBody)
	if err != nil {
		logger.Print(logger.ERROR, "Marshal failed", err)
		return nil, err
	}

	resp, err := transport.SendRequestToProviderServer(
		"POST",
		"/provider/weaviate/search/query",
		nil,
		bytes.NewBuffer(body),
	)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func SearchSimilar(className, query, searchType, fusionType, filters string, fields []string, regulator, maxVectorDistance float32, limit int) ([]byte, error) {
	reqBody := SearchSimilarRequest{
		ClassName:         className,
		Query:             query,
		SearchType:        searchType,
		Filters:           filters,
		Fields:            fields,
		Regulator:         &regulator,
		Limit:             limit,
		FusionType:        fusionType,
		MaxVectorDistance: &maxVectorDistance,
	}

	body, err := json.Marshal(reqBody)
	if err != nil {
		logger.Print(logger.ERROR, "Marshal failed", err)
		return nil, err
	}

	resp, err := transport.SendRequestToProviderServer(
		"POST",
		"/provider/weaviate/search/similar",
		nil,
		bytes.NewBuffer(body),
	)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func SearchSimilarBulk(reqs []SearchSimilarRequest) ([]byte, error) {
	body, err := json.Marshal(reqs)
	if err != nil {
		logger.Print(logger.ERROR, "Marshal failed", err)
		return nil, err
	}

	resp, err := transport.SendRequestToProviderServer(
		"POST",
		"/provider/weaviate/search/similar/bulk",
		nil,
		bytes.NewBuffer(body),
	)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func GetCount(className string, filters string) ([]byte, error) {
	reqBody := CountRequest{
		ClassName: className,
		Filters:   filters,
	}

	body, err := json.Marshal(reqBody)
	if err != nil {
		logger.Print(logger.ERROR, "Marshal failed", err)
		return nil, err
	}

	resp, err := transport.SendRequestToProviderServer(
		"POST",
		"/provider/weaviate/count",
		nil,
		bytes.NewBuffer(body),
	)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
