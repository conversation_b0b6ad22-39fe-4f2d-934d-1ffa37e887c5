package context

import (
	"regexp"
	"slices"
	"strings"

	"github.com/precize/common"
	contextutils "github.com/precize/common/context"
	resourceutils "github.com/precize/enhancer/internal/resource"
)

func IsCostCenterKey(tagKey string) bool {

	for r := range contextutils.CostCenterTagKeys {
		regex := regexp.MustCompile(r)
		if regex.MatchString(strings.ToLower(tagKey)) {
			return true
		}
	}

	return false
}

func GetInheritedCostCenter(parentDoc common.ResourceContextInsertDoc) (inheritedCostCenter []common.ResourceContextItem) {

	if len(parentDoc.DefinedCostCenter) > 0 {
		inheritedCostCenter = append(inheritedCostCenter, parentDoc.DefinedCostCenter...)
	} else if len(parentDoc.InheritedCostCenter) > 0 {
		inheritedCostCenter = append(inheritedCostCenter, parentDoc.InheritedCostCenter...)
	}

	return
}

func GetUniqueCostCenterContext(resourceContextDoc *common.ResourceContextInsertDoc) (costCenter []string) {

	uniqueCostCenter := make(map[string]struct{})

	resourceutils.GetUniqueContext(&resourceContextDoc.DefinedCostCenter, uniqueCostCenter)
	resourceutils.GetUniqueContext(&resourceContextDoc.InheritedCostCenter, uniqueCostCenter)

	for costCenterName := range uniqueCostCenter {
		costCenter = append(costCenter, costCenterName)
	}

	return
}

func GetStaticDescriptionOfCostCenterType(costCenterType string) (desc string) {

	switch costCenterType {
	case common.PARENT_PATH_COSTCENTER_TYPE:
		desc = "CostCenter has been derived from parent path of resource"
	case common.CUSTOMER_DEFINED_COSTCENTER_TYPE:
		desc = "CostCenter has been assigned from Precize console"
	case common.PRECIZE_DEFINED_COSTCENTER_TYPE:
		desc = "CostCenter has been assigned by Precize"
	}

	if len(desc) == 0 && strings.Contains(costCenterType, contextutils.TAG_PREFIX) {
		desc = "CostCenter has been derived from tag"
	}

	return
}

func EnhanceContextItemForCostCenter(resourceContextDoc *common.ResourceContextInsertDoc, costCenter []string) {

	for i, definedCostCenter := range resourceContextDoc.ResourceCostCenterTypes.DefinedCostCenter {
		if slices.Contains(costCenter, definedCostCenter.Name) {
			definedCostCenter.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
		}

		if len(definedCostCenter.Desc) == 0 {
			definedCostCenter.Desc = GetStaticDescriptionOfCostCenterType(definedCostCenter.Type)
		}

		resourceContextDoc.ResourceCostCenterTypes.DefinedCostCenter[i] = definedCostCenter
	}

	for i, inheritedCostCenter := range resourceContextDoc.ResourceCostCenterTypes.InheritedCostCenter {
		if slices.Contains(costCenter, inheritedCostCenter.Name) {
			inheritedCostCenter.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
		}

		if len(inheritedCostCenter.Desc) == 0 {
			inheritedCostCenter.Desc = GetStaticDescriptionOfCostCenterType(inheritedCostCenter.Type)
		}

		resourceContextDoc.ResourceCostCenterTypes.InheritedCostCenter[i] = inheritedCostCenter
	}
}
