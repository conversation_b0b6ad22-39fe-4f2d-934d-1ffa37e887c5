package context

import (
	"regexp"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	contextutils "github.com/precize/common/context"
	"github.com/precize/elastic"
	resourceutils "github.com/precize/enhancer/internal/resource"
	"github.com/precize/enhancer/rcontext"
	"github.com/precize/logger"
)

func IsTTLKey(tagKey string) bool {

	for ttlKey := range contextutils.TTLTagKeys {

		if strings.Contains(ttlKey, `\b`) {
			// \b whole word match does not consider _ as special character
			tagKey = strings.ReplaceAll(tagKey, "_", "-")
		}

		regex := regexp.MustCompile(ttlKey)
		if regex.MatchString(strings.ToLower(tagKey)) {
			return true
		}
	}

	return false
}

func GetTTLFromResourceName(resourceContext *rcontext.ResourceContext, resourceContextInsertDoc *common.ResourceContextInsertDoc, entityJSON map[string]any) {

	ttlValue := GetTTLFromValue(resourceContextInsertDoc.ResourceName, false, resourceutils.GetCreateTimeOfResource(entityJSON), resourceContext.LastCollectedAt)

	if len(ttlValue) > 0 {
		resourceContextInsertDoc.ResourceTTLTypes.DerivedTTL = append(resourceContextInsertDoc.ResourceTTLTypes.DerivedTTL,
			common.ResourceContextItem{
				Name: ttlValue,
				Type: common.RESOURCE_NAME_TTL_TYPE,
				Desc: GetStaticDescriptionOfTTLType(common.RESOURCE_NAME_TTL_TYPE),
			})
	}
}

func GetTTLFromValue(str string, tag bool, createTime time.Time, lastCollectedAt string) (ttlValue string) {

	if tag {
		if t, ok := common.TryParseTime(str); ok {
			ttlValue = elastic.DateTime(t)
			return
		}
	}

	ttlValue = GetTTLFromNonTimeValue(strings.ToLower(str), createTime, lastCollectedAt)

	if tag && len(ttlValue) == 0 {
		logger.Print(logger.INFO, "Unknown TTL value", str)
	}

	return
}

func GetTTLFromNonTimeValue(str string, createTime time.Time, lastCollectedAt string) (ttl string) {

	for ttlValue := range contextutils.TTLDeleteValues {

		if strings.Contains(ttlValue, `\b`) {
			// \b whole word match does not consider _ as special character
			str = strings.ReplaceAll(str, "_", "-")
		}

		regex := regexp.MustCompile(ttlValue)
		if regex.MatchString(str) {

			if !createTime.IsZero() {
				// Two weeks from create time
				ttl = elastic.DateTime(createTime.Add(14 * 24 * time.Hour))

				return
			} else {

				if lastCollected, err := strconv.ParseInt(lastCollectedAt, 10, 64); err == nil {

					// lastCollectedAt is epoch millis
					// One second before lastCollectedAt
					ttl = elastic.DateTime(time.UnixMilli(lastCollected - 1000))

					return
				}
			}
		}
	}

	for ttlValue := range contextutils.TTLDoNotDeleteValues {

		if strings.Contains(ttlValue, `\b`) {
			// \b whole word match does not consider _ as special character
			str = strings.ReplaceAll(str, "_", "-")
		}

		regex := regexp.MustCompile(ttlValue)
		if regex.MatchString(str) {
			ttl = elastic.DateTime(common.MAX_TIME)
			return
		}
	}

	if str == "-1" {
		ttl = elastic.DateTime(common.MAX_TIME)
		return
	}

	return
}

func GetUniqueTTLContext(resourceContextDoc *common.ResourceContextInsertDoc) (ttl []string) {

	uniqueTTL := make(map[string]struct{})

	resourceutils.GetUniqueContext(&resourceContextDoc.DefinedTTL, uniqueTTL)
	resourceutils.GetUniqueContext(&resourceContextDoc.DerivedTTL, uniqueTTL)
	resourceutils.GetUniqueContext(&resourceContextDoc.InheritedTTL, uniqueTTL)

	for ttlValue := range uniqueTTL {
		ttl = append(ttl, ttlValue)
	}

	return
}

func GetStaticDescriptionOfTTLType(ttlType string) (desc string) {

	switch ttlType {
	case common.RESOURCE_NAME_TTL_TYPE:
		desc = "TTL has been derived from resource name"
	case common.CUSTOMER_DEFINED_TTL_TYPE:
		desc = "TTL has been assigned from Precize console"
	case common.PRECIZE_DEFINED_TTL_TYPE:
		desc = "TTL has been assigned by Precize"
	}

	if len(desc) == 0 && strings.Contains(ttlType, contextutils.TAG_PREFIX) {
		desc = "TTL has been derived from tag"
	}

	return
}

func EnhanceContextItemForTTL(resourceContextDoc *common.ResourceContextInsertDoc, ttl []string) {

	for i, definedTTL := range resourceContextDoc.ResourceTTLTypes.DefinedTTL {
		if slices.Contains(ttl, definedTTL.Name) {
			definedTTL.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
		}

		if len(definedTTL.Desc) == 0 {
			definedTTL.Desc = GetStaticDescriptionOfSoftwareType(definedTTL.Type)
		}

		resourceContextDoc.ResourceTTLTypes.DefinedTTL[i] = definedTTL
	}

	for i, derivedTTL := range resourceContextDoc.ResourceTTLTypes.DerivedTTL {
		if slices.Contains(ttl, derivedTTL.Name) {
			derivedTTL.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
		}

		if len(derivedTTL.Desc) == 0 {
			derivedTTL.Desc = GetStaticDescriptionOfSoftwareType(derivedTTL.Type)
		}

		resourceContextDoc.ResourceTTLTypes.DerivedTTL[i] = derivedTTL
	}

	for i, inheritedTTL := range resourceContextDoc.ResourceTTLTypes.InheritedTTL {
		if slices.Contains(ttl, inheritedTTL.Name) {
			inheritedTTL.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
		}

		if len(inheritedTTL.Desc) == 0 {
			inheritedTTL.Desc = GetStaticDescriptionOfSoftwareType(inheritedTTL.Type)
		}

		resourceContextDoc.ResourceTTLTypes.InheritedTTL[i] = inheritedTTL
	}
}
