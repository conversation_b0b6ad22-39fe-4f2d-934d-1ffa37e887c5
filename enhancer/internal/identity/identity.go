package identityutils

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/precize/common"
	contextutils "github.com/precize/common/context"
	"github.com/precize/elastic"
	emailutils "github.com/precize/enhancer/internal/email"
	"github.com/precize/enhancer/internal/sddl"
	"github.com/precize/enhancer/rcontext"
	"github.com/precize/logger"
)

func FetchAllRelatedIdentities(resourceContext *rcontext.ResourceContext, identityID string) ([]common.IdentitiesDoc, error) {

	var serviceCode string
	if sCode, ok := common.IdStrToCspStrMap[resourceContext.ServiceID]; !ok {
		return nil, fmt.Errorf("service code not found for service id %s", resourceContext.ServiceID)
	} else {
		serviceCode = sCode
	}

	identityQuery := `{
		"query": {
			"bool": {
				"must": [
					{"match": {"tenantId.keyword": "` + resourceContext.TenantID + `"}},
					{"match": {"deleted": false}},
					{"match":{"serviceCode":"` + serviceCode + `"}}
				],
				"must_not": [
					{"terms": {"type.keyword": ["BITBUCKET_IDENTITY","GITLAB_IDENTITY","GITHUB_IDENTITY","TAGGED_IDENTITY","DEFINED_IDENTITY","ACTIVITY_IDENTITY","DERIVED_IDENTITY","BITBUCKET_USER","GITLAB_USER","GITHUB_USER","TAGGED_USER","DEFINED_USER","ACTIVITY_USER","DERIVED_USER"]}}
				],
				"should": [
					{"match": {"identityId.keyword": "` + identityID + `"}},
					{"match": {"primaryEmail.keyword": "` + identityID + `"}}
				],
				"minimum_should_match": 1
			}
		}
	}`

	identityMaps, err := elastic.ExecuteSearchQuery([]string{elastic.IDENTITIES_INDEX}, identityQuery)
	if err != nil {
		return nil, err
	}

	var identityDocs []common.IdentitiesDoc
	for _, identityMap := range identityMaps {
		var identity common.IdentitiesDoc
		jsonBytes, err := json.Marshal(identityMap)
		if err != nil {
			logger.Print(logger.ERROR, "Error marshalling identity to JSON", err)
			continue
		}

		if err = json.Unmarshal(jsonBytes, &identity); err != nil {
			logger.Print(logger.ERROR, "Error unmarshalling JSON to IdentitiesDoc", err)
			continue
		}

		identityDocs = append(identityDocs, identity)
	}

	return identityDocs, nil
}

func HandleChildEmail(r *rcontext.ResourceContext, temp *rcontext.UserContext) {
	var (
		primaryDomain    string
		primaryDomainMap = make(map[string]struct{})
	)

	if len(r.PrimaryDomains) > 0 {
		primaryDomain = r.PrimaryDomains[0]

		for _, primaryDomain := range r.PrimaryDomains {
			primaryDomainMap[primaryDomain] = struct{}{}
		}
	}

	if !strings.HasSuffix(strings.ToLower(temp.Email), strings.ToLower(primaryDomain)) {
		if _, ok := r.GetChildPrimaryEmail(temp.Email); !ok {
			primaryEmail := emailutils.DerivePrimaryIdentityFromPartner(temp.Email, temp.Name, r)

			iterations := 0
			for {
				iterations++
				if len(primaryEmail) > 0 && !strings.HasSuffix(strings.ToLower(primaryEmail), strings.ToLower(primaryDomain)) {
					primaryEmail = emailutils.DerivePrimaryIdentityFromPartner(primaryEmail, temp.Name, r)
				} else {
					break
				}

				if iterations > 10 {
					logger.Print(logger.INFO, "Too many iterations in HandleChildEmail", []string{r.TenantID}, temp.Email, primaryEmail)
					break
				}
			}

			if len(primaryEmail) > 0 && temp.Email != primaryEmail {
				r.SetChildPrimaryEmail(temp.Email, primaryEmail, false)

				if _, ok := primaryDomainMap[temp.Email]; ok {
					return
				}

				// if primary identity of partner is undeliverable, then partner email will also be undeliverable
				// vice-versa is being handled by platform
				if isPrimaryActive, ok := r.GetEmailStatus(primaryEmail); ok {
					if !isPrimaryActive {
						temp.Active = false
					}
				}
			}
		}
	}
}

func HandleIdentityWithExternalEmail(resourceContext *rcontext.ResourceContext, temp *rcontext.UserContext) {

	var (
		isValidEmail, isValidEmailDerived bool
		originalEmail                     = temp.Email
	)

	emailWithoutExt := emailutils.ExtractExternalEmail(temp.Email)

	if addr, err := common.ParseAddress(emailWithoutExt); err == nil {

		temp.Email = addr.Address
		temp.IsSddl = sddl.HandleUserContextWithSDDL(&temp.Email, &temp.Name, resourceContext)

		if isValidEmail, isValidEmailDerived = resourceContext.GetEmailStatus(temp.Email); !isValidEmailDerived {
			emailNameMap := map[string]string{
				temp.Email: temp.Name,
			}

			emailStatusMap, err := emailutils.CheckEmailValidity(emailNameMap, false, resourceContext)
			if err != nil {
				return
			}

			if len(emailStatusMap) > 0 {
				isValidEmail = emailStatusMap[temp.Email]
				resourceContext.SetEmailStatus(temp.Email, isValidEmail)
			}

			if fullName, ok := resourceContext.GetEmailToFullName(temp.Email); ok && len(fullName) > len(temp.Name) {
				temp.Name = fullName
			}

			if len(temp.Name) <= 0 {
				temp.Name = common.GetFormattedNameFromEmail(temp.Email)
			}

		}

		temp.Active = isValidEmail
		resourceContext.SetChildPrimaryEmail(originalEmail, temp.Email, true)
	}
}

func HandlePersonalAndMicrosoftEmail(resourceContext *rcontext.ResourceContext, temp *rcontext.UserContext) {

	var (
		matchedEmail  string
		name          string
		originalEmail = temp.Email
	)

	if strings.Contains(temp.Email, contextutils.LIVE_MICROSOFT_KEYWORD) {
		parts := strings.Split(temp.Email, contextutils.LIVE_MICROSOFT_KEYWORD)
		if len(parts) > 1 {
			matchedEmail = parts[1]
			matchedEmail = strings.ReplaceAll(matchedEmail, "@", "_")
		}
	} else if strings.Contains(temp.Email, contextutils.MAIL_MICROSOFT_KEYWORD) {
		parts := strings.Split(temp.Email, contextutils.MAIL_MICROSOFT_KEYWORD)
		if len(parts) > 1 {
			matchedEmail = parts[1]
			matchedEmail = strings.ReplaceAll(matchedEmail, "@", "_")
		}
	}

	identityQuery := `{"query":{"bool":{"must":[{"match":{"deleted":"false"}},{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"type.keyword":"AD_USER"}},{"wildcard":{"identityId.keyword":"*` + matchedEmail + `*"}}]}},"size":"1","from":0}`
	identityDocs, err := elastic.ExecuteSearchQuery([]string{elastic.IDENTITIES_INDEX}, identityQuery)
	if err != nil {
		return
	}

	for _, identityDoc := range identityDocs {

		var identity common.IdentitiesDoc
		jsonBytes, err := json.Marshal(identityDoc)
		if err != nil {
			return
		}
		err = json.Unmarshal(jsonBytes, &identity)
		if err != nil {
			return
		}
		matchedEmail = identity.IdentityID
		name = identity.Name
	}

	if addr, err := common.ParseAddress(matchedEmail); err == nil {

		var (
			isValidEmail, isValidEmailDerived bool
			emailStatusMap                    map[string]bool
		)

		temp.Email = addr.Address
		temp.Name = name
		temp.IsSddl = sddl.HandleUserContextWithSDDL(&temp.Email, &name, resourceContext)

		if isValidEmail, isValidEmailDerived = resourceContext.GetEmailStatus(temp.Email); isValidEmailDerived {
			emailNameMap := map[string]string{
				temp.Email: name,
			}

			emailStatusMap, err = emailutils.CheckEmailValidity(emailNameMap, false, resourceContext)
			if err != nil {
				return
			}

			if len(emailStatusMap) > 0 {
				isValidEmail = emailStatusMap[temp.Email]
				resourceContext.SetEmailStatus(temp.Email, isValidEmail)
			}

			if fullName, ok := resourceContext.GetEmailToFullName(temp.Email); ok && len(fullName) > len(temp.Name) {
				temp.Name = fullName
			}

			if len(temp.Name) <= 0 {
				temp.Name = common.GetFormattedNameFromEmail(temp.Email)
			}

		}

		temp.Active = isValidEmail
		resourceContext.SetChildPrimaryEmail(originalEmail, temp.Email, true)
	}

	return
}
