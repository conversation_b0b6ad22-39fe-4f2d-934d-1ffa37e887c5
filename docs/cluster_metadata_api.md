# Cluster Metadata API

This document describes the new cluster metadata API endpoint that provides information about the Weaviate cluster.

## Endpoint

**POST** `/provider/weaviate/cluster/metadata`

## Description

Retrieves metadata information about the Weaviate cluster, including version, modules, and configuration details.

## Request

### Headers
- `Content-Type: application/json`

### Body
```json
{}
```
*Note: The request body can be empty as no parameters are required.*

## Response

### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Successfully retrieved cluster metadata",
  "data": {
    "hostname": "http://[::]:8080",
    "version": "1.22.4",
    "modules": {
      "text2vec-openai": {
        "version": "1.22.4",
        "type": "text2vec"
      }
    }
  }
}
```

### Error Response (500 Internal Server Error)
```json
{
  "success": false,
  "message": "failed to get cluster metadata: connection refused"
}
```

## Usage Examples

### Using the Common Package Helper

```go
import "github.com/precize/common/weaviate"

resp, err := weaviate.GetClusterMetadata()
if err != nil {
    log.Printf("Error: %v", err)
    return
}

fmt.Printf("Response: %s\n", string(resp))
```

### Using HTTP Client Directly

```bash
curl -X POST http://localhost:19090/provider/weaviate/cluster/metadata \
  -H "Content-Type: application/json" \
  -d '{}'
```

### Using the Weaviate Service Directly

```go
import (
    "context"
    "github.com/precize/common/weaviate"
    weaviateService "github.com/precize/pserver/server/weaviate/service"
)

service := weaviateService.NewWeaviateService(weaviateClient)
req := weaviate.ClusterMetadataRequest{}
response, err := service.GetClusterMetadata(context.Background(), req)
```

## Implementation Details

The API is implemented using the following components:

1. **Request Type**: `weaviate.ClusterMetadataRequest` (empty struct)
2. **Service Method**: `WeaviateService.GetClusterMetadata()`
3. **Controller Method**: `WeaviateController.GetClusterMetadata()`
4. **Helper Function**: `weaviate.GetClusterMetadata()`

The implementation uses the Weaviate Go client's `Misc().MetaGetter()` method to retrieve cluster information.

## Error Handling

The API handles the following error scenarios:

- **Connection errors**: When Weaviate cluster is unreachable
- **Invalid JSON**: When request body contains malformed JSON
- **Method not allowed**: When using HTTP methods other than POST
- **Internal errors**: Any other unexpected errors during processing

All errors are logged and returned with appropriate HTTP status codes and descriptive error messages.
