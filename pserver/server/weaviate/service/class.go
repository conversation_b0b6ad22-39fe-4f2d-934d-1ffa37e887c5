package service

import (
	"context"
	"fmt"
	"strings"

	"github.com/precize/common/weaviate"
	"github.com/precize/logger"
	"github.com/precize/pserver/server/generic/model"
	weaviateInternalModel "github.com/weaviate/weaviate/entities/models"
)

func (s *WeaviateService) CreateClass(ctx context.Context, req weaviate.CreateClassRequest) (model.Response, error) {
	exists, err := s.client.Schema().ClassExistenceChecker().WithClassName(req.ClassName).Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to check class existence: %v", err)
		return model.Response{}, fmt.Errorf("failed to check class existence: %w", err)
	}

	if exists {
		logger.Print(logger.INFO, "Class '%s' already exists, skipping creation", req.ClassName)
		return model.Response{
			Success: true,
			Message: fmt.Sprintf("Class '%s' already exists, skipping creation", req.ClassName),
		}, nil
	}

	vectorConfig := make(map[string]weaviateInternalModel.VectorConfig)
	for vectorName, config := range req.VectorConfig {
		vectorConfig[vectorName] = weaviateInternalModel.VectorConfig{
			Vectorizer:        config.Vectorizer,
			VectorIndexType:   config.VectorIndexType,
			VectorIndexConfig: config.VectorIndexConfig,
		}
	}

	class := &weaviateInternalModel.Class{
		Class:        req.ClassName,
		Description:  req.Description,
		VectorConfig: vectorConfig,
	}

	if len(req.Properties) > 0 {
		var properties []*weaviateInternalModel.Property
		for _, prop := range req.Properties {
			properties = append(properties, &weaviateInternalModel.Property{
				Name:     prop.Name,
				DataType: prop.DataType,
			})
		}
		class.Properties = properties
	}

	err = s.client.Schema().ClassCreator().WithClass(class).Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to create class: %v", err)
		return model.Response{}, fmt.Errorf("failed to create class: %w", err)
	}

	return model.Response{
		Success: true,
		Message: fmt.Sprintf("Successfully created class '%s'", req.ClassName),
	}, nil
}

func (s *WeaviateService) DeleteClass(ctx context.Context, req weaviate.DeleteClassRequest) (model.Response, error) {
	err := s.client.Schema().ClassDeleter().WithClassName(req.ClassName).Do(ctx)
	if err != nil {
		if strings.Contains(err.Error(), "400") || strings.Contains(strings.ToLower(err.Error()), "not found") {
			logger.Print(logger.INFO, "Class '%s' does not exist, deletion skipped", req.ClassName)
			return model.Response{
				Success: true,
				Message: fmt.Sprintf("Class '%s' does not exist, deletion skipped", req.ClassName),
			}, nil
		}
		logger.Print(logger.ERROR, "Failed to delete class: %v", err)
		return model.Response{}, fmt.Errorf("failed to delete class: %w", err)
	}

	return model.Response{
		Success: true,
		Message: fmt.Sprintf("Successfully deleted class '%s'", req.ClassName),
	}, nil
}

func (s *WeaviateService) GetClass(ctx context.Context, req weaviate.GetClassRequest) (model.Response, error) {
	class, err := s.client.Schema().ClassGetter().WithClassName(req.ClassName).Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to get class: %v", err)
		return model.Response{}, fmt.Errorf("failed to get class: %w", err)
	}

	return model.Response{
		Success: true,
		Message: fmt.Sprintf("Successfully retrieved class '%s'", req.ClassName),
		Data:    class,
	}, nil
}
