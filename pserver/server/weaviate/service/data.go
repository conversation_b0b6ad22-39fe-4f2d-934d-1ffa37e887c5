package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"github.com/go-openapi/strfmt"
	"github.com/google/uuid"
	"github.com/precize/common/weaviate"
	"github.com/precize/config"
	"github.com/precize/logger"
	"github.com/precize/pserver/server/generic/model"
	weaviateInternalModel "github.com/weaviate/weaviate/entities/models"
)

func (s *WeaviateService) InsertData(ctx context.Context, req weaviate.InsertDataRequest) (model.Response, error) {
	batcher := s.client.Batch().ObjectsBatcher()
	for _, obj := range req.Objects {
		batcher = batcher.WithObjects(&weaviateInternalModel.Object{
			Class:      req.ClassName,
			Properties: obj,
		})
	}

	batchRes, err := batcher.Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to execute batch insert: %v", err)
		return model.Response{}, fmt.Errorf("failed to insert data: %w", err)
	}

	var errorMsgs []string
	for _, res := range batchRes {
		if res.Result.Errors != nil {
			for _, batchErr := range res.Result.Errors.Error {
				if batchErr != nil {
					errorMsgs = append(errorMsgs, fmt.Sprintf("%v", *batchErr))
				}
			}
		}
	}

	if len(errorMsgs) > 0 {
		logger.Print(logger.ERROR, "Some objects failed to insert: %v", errorMsgs)
		return model.Response{}, fmt.Errorf("some objects failed to insert: %v", errorMsgs)
	}

	return model.Response{
		Success: true,
		Message: fmt.Sprintf("Successfully inserted %d objects into class '%s'", len(req.Objects), req.ClassName),
	}, nil
}

func (s *WeaviateService) DeleteByID(ctx context.Context, req weaviate.DeleteByIDRequest) (model.Response, error) {
	err := s.client.Data().Deleter().WithClassName(req.ClassName).WithID(req.ID).Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to delete object: %v", err)
		return model.Response{}, fmt.Errorf("failed to delete object: %w", err)
	}

	return model.Response{
		Success: true,
		Message: fmt.Sprintf("Successfully deleted object with ID '%s' from class '%s'", req.ID, req.ClassName),
	}, nil
}

func (s *WeaviateService) DeleteByQuery(ctx context.Context, req weaviate.DeleteByQueryRequest) (model.Response, error) {
	if req.Output == "" {
		req.Output = "minimal"
	}

	payload := map[string]any{
		"match": map[string]any{
			"class": req.ClassName,
		},
		"output": req.Output,
	}

	if len(req.Filters) > 0 {
		payload["match"].(map[string]any)["where"] = req.Filters
	}

	body, err := json.Marshal(payload)
	if err != nil {
		return model.Response{}, fmt.Errorf("failed to marshal delete payload: %w", err)
	}

	url := fmt.Sprintf("%s/v1/batch/objects", config.AppConfig.Weaviate.Scheme+"://"+config.AppConfig.Weaviate.Host)
	reqHttp, err := http.NewRequestWithContext(ctx, http.MethodDelete, url, bytes.NewReader(body))
	if err != nil {
		return model.Response{}, fmt.Errorf("failed to create delete request: %w", err)
	}
	reqHttp.Header.Set("Content-Type", "application/json")

	resp, err := http.DefaultClient.Do(reqHttp)
	if err != nil {
		return model.Response{}, fmt.Errorf("failed to execute delete request: %w", err)
	}
	defer resp.Body.Close()

	var result map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return model.Response{}, fmt.Errorf("failed to parse delete response: %w", err)
	}

	return model.Response{
		Success: resp.StatusCode == http.StatusOK,
		Message: fmt.Sprintf("Executed delete by query on class '%s'", req.ClassName),
		Data:    result,
	}, nil
}

func (s *WeaviateService) InsertDataByID(ctx context.Context, req weaviate.InsertDataRequest) (model.Response, error) {
	batcher := s.client.Batch().ObjectsBatcher()
	for _, obj := range req.Objects {
		var id strfmt.UUID
		if objId, ok := obj["id"].(string); ok {
			parsedUUID := strfmt.UUID(objId)

			if _, err := uuid.Parse(string(parsedUUID)); err != nil {
				logger.Print(logger.ERROR, "Failed to parse UUID: %v", err)
				return model.Response{}, fmt.Errorf("failed to parse UUID: %w", err)
			}
			id = parsedUUID
			delete(obj, "id")
		}

		insertObj := &weaviateInternalModel.Object{
			Class:      req.ClassName,
			Properties: obj,
		}

		if len(id) > 0 {
			insertObj.ID = id
		}

		batcher = batcher.WithObjects(insertObj)
	}

	batchRes, err := batcher.Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to execute batch insert: %v", err)
		return model.Response{}, fmt.Errorf("failed to insert data: %w", err)
	}

	var errorMsgs []string
	for _, res := range batchRes {
		if res.Result.Errors != nil {
			for _, batchErr := range res.Result.Errors.Error {
				if batchErr != nil {
					errorMsgs = append(errorMsgs, fmt.Sprintf("%v", *batchErr))
				}
			}
		}
	}

	if len(errorMsgs) > 0 {
		logger.Print(logger.ERROR, "Some objects failed to insert: %v", errorMsgs)
		return model.Response{}, fmt.Errorf("some objects failed to insert: %v", errorMsgs)
	}

	return model.Response{
		Success: true,
		Message: fmt.Sprintf("Successfully inserted %d objects into class '%s'", len(req.Objects), req.ClassName),
	}, nil
}

func (s *WeaviateService) GetCount(ctx context.Context, req weaviate.CountRequest) (model.Response, error) {
	var args []string

	if req.Filters != "" {
		args = append(args, fmt.Sprintf("where: %s", req.Filters))
	}

	argStr := ""
	if len(args) > 0 {
		argStr = fmt.Sprintf("(%s)", strings.Join(args, ", "))
	}

	rawQuery := fmt.Sprintf(`
		query {
			Aggregate {
				%s%s {
					meta {
						count
					}
				}
			}
		}`,
		req.ClassName,
		argStr,
	)

	result, err := s.client.GraphQL().Raw().WithQuery(rawQuery).Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "GetCount failed: %v", err)
		return model.Response{}, fmt.Errorf("failed to get record count: %w", err)
	}

	return model.Response{
		Success: true,
		Message: fmt.Sprintf("Successfully retrieved count for class '%s'", req.ClassName),
		Data:    result,
	}, nil
}

func (s *WeaviateService) GetClusterMetadata(ctx context.Context, req weaviate.ClusterMetadataRequest) (model.Response, error) {
	meta, err := s.client.Misc().MetaGetter().Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to get cluster metadata: %v", err)
		return model.Response{}, fmt.Errorf("failed to get cluster metadata: %w", err)
	}

	return model.Response{
		Success: true,
		Message: "Successfully retrieved cluster metadata",
		Data:    meta,
	}, nil
}
