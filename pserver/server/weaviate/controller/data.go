package controller

import (
	"net/http"

	"github.com/precize/common/weaviate"
	"github.com/precize/pserver/server/weaviate/utils"
)

func (c *WeaviateController) InsertData(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		utils.WriteError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req weaviate.InsertDataRequest
	if err := utils.ParseJSON(r, &req); err != nil {
		utils.WriteError(w, http.StatusBadRequest, "Invalid JSON format")
		return
	}

	if req.ClassName == "" || len(req.Objects) == 0 {
		utils.WriteError(w, http.StatusBadRequest, "className and objects are required")
		return
	}

	response, err := c.service.InsertData(r.Context(), req)
	if err != nil {
		utils.WriteError(w, http.StatusInternalServerError, err.Error())
		return
	}

	utils.WriteResponse(w, http.StatusOK, response)
}

func (c *WeaviateController) DeleteByID(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		utils.WriteError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req weaviate.DeleteByIDRequest
	if err := utils.ParseJSON(r, &req); err != nil {
		utils.WriteError(w, http.StatusBadRequest, "Invalid JSON format")
		return
	}

	if req.ClassName == "" || req.ID == "" {
		utils.WriteError(w, http.StatusBadRequest, "className and id are required")
		return
	}

	response, err := c.service.DeleteByID(r.Context(), req)
	if err != nil {
		if utils.IsNotFoundError(err) {
			utils.WriteError(w, http.StatusNotFound, "Object not found")
			return
		}
		utils.WriteError(w, http.StatusInternalServerError, err.Error())
		return
	}

	utils.WriteResponse(w, http.StatusOK, response)
}

func (c *WeaviateController) DeleteByQuery(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		utils.WriteError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req weaviate.DeleteByQueryRequest
	if err := utils.ParseJSON(r, &req); err != nil {
		utils.WriteError(w, http.StatusBadRequest, "Invalid JSON format")
		return
	}

	if req.ClassName == "" || len(req.Filters) == 0 {
		utils.WriteError(w, http.StatusBadRequest, "className and filters are required")
		return
	}

	response, err := c.service.DeleteByQuery(r.Context(), req)
	if err != nil {
		utils.WriteError(w, http.StatusInternalServerError, err.Error())
		return
	}

	utils.WriteResponse(w, http.StatusOK, response)
}

func (c *WeaviateController) InsertDataByID(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		utils.WriteError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req weaviate.InsertDataRequest
	if err := utils.ParseJSON(r, &req); err != nil {
		utils.WriteError(w, http.StatusBadRequest, "Invalid JSON format")
		return
	}

	if req.ClassName == "" || len(req.Objects) == 0 {
		utils.WriteError(w, http.StatusBadRequest, "className and objects are required")
		return
	}

	response, err := c.service.InsertDataByID(r.Context(), req)
	if err != nil {
		utils.WriteError(w, http.StatusInternalServerError, err.Error())
		return
	}

	utils.WriteResponse(w, http.StatusOK, response)
}

func (c *WeaviateController) GetClusterMetadata(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		utils.WriteError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req weaviate.ClusterMetadataRequest
	if err := utils.ParseJSON(r, &req); err != nil {
		utils.WriteError(w, http.StatusBadRequest, "Invalid JSON format")
		return
	}

	response, err := c.service.GetClusterMetadata(r.Context(), req)
	if err != nil {
		utils.WriteError(w, http.StatusInternalServerError, err.Error())
		return
	}

	utils.WriteResponse(w, http.StatusOK, response)
}

func (c *WeaviateController) GetCount(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		utils.WriteError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req weaviate.CountRequest
	if err := utils.ParseJSON(r, &req); err != nil {
		utils.WriteError(w, http.StatusBadRequest, "Invalid JSON format")
		return
	}

	if req.ClassName == "" {
		utils.WriteError(w, http.StatusBadRequest, "className is required")
		return
	}

	response, err := c.service.GetCount(r.Context(), req)
	if err != nil {
		utils.WriteError(w, http.StatusInternalServerError, err.Error())
		return
	}

	utils.WriteResponse(w, http.StatusOK, response)
}
