package metadata

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/precize/common/basic/uuid"
	resourceutils "github.com/precize/common/resource"
	"github.com/precize/logger"
)

const (
	MAX_RECORDS = 100
)

func VectorizeResourceMetadata(resourceDocs []map[string]any, tenantID string) error {

	var (
		records []ResourceMetadata
	)

	for _, resourceDoc := range resourceDocs {

		rscDocBytes, err := json.Marshal(resourceDoc)
		if err != nil {
			logger.Print(logger.ERROR, "Error marshalling alert data to JSON", []string{tenantID}, err)
			continue
		}

		var crDoc resourceutils.CloudResource
		if err = json.Unmarshal(rscDocBytes, &crDoc); err != nil {
			logger.Print(logger.ERROR, "Error unmarshalling JSON", []string{tenantID}, err)
			continue
		}

		resourceName := crDoc.ResourceName

		if len(resourceName) <= 0 {
			resourceName = FetchResourceNameFromCRDoc(crDoc)
		}

		var tagParts []string
		for _, t := range crDoc.Tags {
			tagParts = append(tagParts, fmt.Sprintf("%s:%s", t.Key, t.Value))
		}
		tagString := strings.Join(tagParts, ", ")

		id := uuid.GenerateUUIDFromString(crDoc.EntityID + crDoc.EntityType + crDoc.TenantID)
		description := fmt.Sprintf("name: %s | tags: %s", resourceName, tagString)

		vectorInsertDoc := ResourceMetadata{
			ID:          id,
			EntityID:    crDoc.EntityID,
			EntityName:  resourceName,
			EntityType:  crDoc.EntityType,
			TenantID:    tenantID,
			ServiceID:   crDoc.ServiceID,
			Description: description,
		}

		records = append(records, vectorInsertDoc)
	}

	if len(records) > 0 {
		if err := InsertResourceMetadata(records); err != nil {
			logger.Print(logger.ERROR, "Error inserting resource metadata", []string{tenantID}, err)
			return err
		}
	}

	return nil
}
