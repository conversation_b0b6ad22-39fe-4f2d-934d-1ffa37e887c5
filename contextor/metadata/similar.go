package metadata

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/common/basic/uuid"
	resourceutils "github.com/precize/common/resource"
	"github.com/precize/common/weaviate"
	"github.com/precize/contextor/neighbour"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func GetSimilarResourceUsingMetadata(resourceDocs []map[string]any, tenantID, serviceID string, bulkContextNeighbourRequest *strings.Builder, contextNeighboursCount *int) {

	similaritySearchReq := []weaviate.SearchSimilarRequest{}
	queryToPrimaryRecordMap := make(map[string]ResourceMetadata)

	count := 0

	for _, resourceDoc := range resourceDocs {
		rscDocBytes, err := json.Marshal(resourceDoc)
		if err != nil {
			logger.Print(logger.ERROR, "Error marshalling alert data to JSON", []string{tenantID}, err)
			continue
		}

		var crDoc resourceutils.CloudResource
		if err = json.Unmarshal(rscDocBytes, &crDoc); err != nil {
			logger.Print(logger.ERROR, "Error unmarshalling JSON", []string{tenantID}, err)
			continue
		}

		resourceName := crDoc.ResourceName

		if len(resourceName) <= 0 {
			resourceName = FetchResourceNameFromCRDoc(crDoc)
		}

		var tagParts []string
		for _, t := range crDoc.Tags {
			tagParts = append(tagParts, fmt.Sprintf("%s:%s", t.Key, t.Value))
		}
		tagString := strings.Join(tagParts, ", ")

		id := uuid.GenerateUUIDFromString(crDoc.EntityID + crDoc.EntityType + crDoc.TenantID)
		searchQuery := fmt.Sprintf("name: %s | tags: %s", resourceName, tagString)
		regulator := float32(0.4)
		maxVectorDistance := float32(0.4)

		if len(tagString) <= 0 {
			if len(resourceName) <= 0 {
				continue
			}

			if strings.ToLower(resourceName) == "default" {
				continue
			}
		} else if len(resourceName) <= 0 {
			continue
		}

		rawFilter := `{operator:And,operands:[
						{operator:Equal,path:["tenantId"],valueText:"` + tenantID + `"},
						{operator:Equal,path:["serviceId"],valueNumber:` + serviceID + `},
						{operator:NotEqual,path:["id"],valueText:"` + id + `"}
					]}`

		similaritySearchReq = append(similaritySearchReq, weaviate.SearchSimilarRequest{
			ClassName:         RESOURCE_METADATA_CLASS,
			Query:             searchQuery,
			SearchType:        "hybrid",
			Fields:            []string{"entityId", "entityName", "entityType", "description", "tenantId", "serviceId", "_additional { score id }"},
			Regulator:         &regulator,
			Limit:             5,
			MaxVectorDistance: &maxVectorDistance,
			Filters:           rawFilter,
		})

		queryToPrimaryRecordMap[fmt.Sprintf("q%d", count)] = ResourceMetadata{
			ID:          id,
			EntityID:    crDoc.EntityID,
			EntityType:  crDoc.EntityType,
			EntityName:  resourceName,
			TenantID:    tenantID,
			ServiceID:   crDoc.ServiceID,
			Description: searchQuery,
		}
		count++
	}

	if len(similaritySearchReq) == 0 {
		return
	}

	similar, err := FetchSimilarResourcesUsingMetadata(similaritySearchReq, tenantID, serviceID)
	if err != nil {
		return
	}

	// TODO: if no similar resource is found try with name only

	for query, similarRscList := range similar {

		var primaryRecord ResourceMetadata
		if pRecord, ok := queryToPrimaryRecordMap[query]; !ok {
			continue
		} else {
			primaryRecord = pRecord
		}

		for _, similarRsc := range similarRscList {

			metadataNeighbourType := "metadata"

			docID := common.GenerateCombinedHashID(primaryRecord.EntityID, similarRsc.EntityID, metadataNeighbourType, tenantID)

			score, _ := strconv.ParseFloat(similarRsc.AdditionalInfo.Score, 64)

			contextNeighbourDoc := neighbour.ContextNeighboursDoc{
				Type: metadataNeighbourType,
				ResourceA: neighbour.NeighbourResource{
					EntityID:   primaryRecord.EntityID,
					EntityType: primaryRecord.EntityType,
					Value:      primaryRecord.Description,
				},
				ResourceB: neighbour.NeighbourResource{
					EntityID:   similarRsc.EntityID,
					EntityType: similarRsc.EntityType,
					Value:      similarRsc.Description,
				},
				Score:      float32(score),
				TenantID:   tenantID,
				ServiceID:  primaryRecord.ServiceID,
				ID:         docID,
				Deleted:    false,
				InsertTime: elastic.DateTime(time.Now()),
			}

			contextNeighbourInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
			contextNeighbourInsertDoc, err := json.Marshal(contextNeighbourDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling document", err)
				continue
			}

			bulkContextNeighbourRequest.WriteString(contextNeighbourInsertMetadata)
			bulkContextNeighbourRequest.WriteString("\n")
			bulkContextNeighbourRequest.Write(contextNeighbourInsertDoc)
			bulkContextNeighbourRequest.WriteString("\n")

			*contextNeighboursCount++
		}
	}
}
