package metadata

type ResourceMetadata struct {
	ID          string `json:"id"`
	EntityID    string `json:"entityId"`
	EntityType  string `json:"entityType"`
	EntityName  string `json:"entityName"`
	TenantID    string `json:"tenantId"`
	ServiceID   int    `json:"serviceId"`
	Description string `json:"description"`
}

type ResourceMetadataSearch struct {
	ID             string         `json:"id"`
	EntityID       string         `json:"entityId"`
	EntityType     string         `json:"entityType"`
	EntityName     string         `json:"entityName"`
	TenantID       string         `json:"tenantId"`
	ServiceID      int            `json:"serviceId"`
	Description    string         `json:"description"`
	AdditionalInfo AdditionalInfo `json:"_additional"`
}

type AdditionalInfo struct {
	Score        string  `json:"score"`
	ExplainScore string  `json:"explainScore"`
	Distance     float64 `json:"distance"`
}

type MetadataSearchResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    struct {
		Data map[string]struct {
			MetadataResp []ResourceMetadataSearch `json:"ResourceMetadata"`
		} `json:"data"`
	} `json:"data"`
}

type MetadataNestedFilter struct {
	Operator  string                 `json:"operator,omitempty"`
	Path      []string               `json:"path,omitempty"`
	ValueText string                 `json:"valueText,omitempty"`
	ValueInt  int                    `json:"valueInt,omitempty"`
	Operands  []MetadataNestedFilter `json:"operands,omitempty"`
}
