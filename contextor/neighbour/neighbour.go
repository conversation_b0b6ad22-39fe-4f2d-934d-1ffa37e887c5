package neighbour

type ContextNeighboursDoc struct {
	Type       string            `json:"type"`
	ResourceA  NeighbourResource `json:"resourceA"`
	ResourceB  NeighbourResource `json:"resourceB"`
	Score      float32           `json:"score"`
	TenantID   string            `json:"tenantId"`
	ServiceID  int               `json:"serviceId"`
	ID         string            `json:"id"`
	Deleted    bool              `json:"deleted"`
	InsertTime string            `json:"insertTime"`
}

type NeighbourResource struct {
	EntityID   string `json:"entityId"`
	EntityType string `json:"entityType"`
	Value      string `json:"value"`
}
