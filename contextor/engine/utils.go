package engine

import (
	"encoding/json"
	"fmt"

	contextutils "github.com/precize/common/context"
	elasticutils "github.com/precize/common/elastic"
	"github.com/precize/common/openai"
	"github.com/precize/contextor/ai"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

type CompanyContext struct {
	CompanyName   string   `json:"company_name"`
	Domains       []string `json:"domains"`
	Applications  []string `json:"applications"`
	Teams         []string `json:"teams"`
	Technologies  []string `json:"technologies"`
	BusinessModel string   `json:"business_model"`
	Notes         string   `json:"notes"`
}

func initializeGlobalSystemPrompt(ce *ContextEngine) error {

	companyName, err := contextutils.GetCompanyName(ce.TenantID)
	if err != nil {
		return err
	}

	convID, err := openai.CreateConversation(ce.AI.APIKey)
	if err != nil {
		return err
	}

	out, err := openai.AskAndRespond(ce.AI.APIKey, convID, "gpt-4o-mini", "user", []string{fmt.Sprintf(ai.CUSTOMER_PROMPT, companyName)}, nil)
	if err != nil {
		return err
	}

	companyContext, err := parseCompanyContext(out)
	if err != nil {
		return err
	}

	companyContextJSON, err := json.Marshal(companyContext)
	if err != nil {
		logger.Print(logger.ERROR, "Marshal error", err)
		return err
	}

	logger.Print(logger.INFO, "Company context", string(companyContextJSON))

	// TODO: Add global apps/teams into system prompt

	ce.SystemPrompt = fmt.Sprintf(ai.INTRO_PROMPT + "\n\nAdditional customer-specific context:\n" + string(companyContextJSON) + "\n\nUse this background knowledge ONLY to understand what this company does and to interpret resources more accurately, focusing on how the customers data and context go hand in hand. DO NOT use this data to extract breadcrumbs or derive context for the resources.")

	return nil
}

func collectResourceData(ce *ContextEngine, searchAfter any) (map[string]map[string]any, any, error) {

	resourcesQuery := `{"_source":["tenantId","entityId","serviceId","entityType","entityJson","tags"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + ce.TenantID + `"}},{"term":{"collectedAt":` + ce.CollectedAt + `}},{"term":{"serviceId":` + ce.ServiceID + `}},{"match":{"isVirtual":"false"}}],"must_not":[{"terms":{"entityType.keyword":[` + elasticutils.GetNonDataResourcesElasticQuery() + `]}}]}}}`

	resourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, resourcesQuery, searchAfter)
	if err != nil {
		return nil, nil, err
	}

	if len(resourcesDocs) > 0 {
		return resourcesDocs, sortResponse, nil
	} else {
		return nil, nil, nil
	}
}

func parseCompanyContext(aiResponse string) (*CompanyContext, error) {

	var ctx CompanyContext

	openai.RemoveJSONQuoteFromResponse(&aiResponse)

	var raw map[string]any
	if err := json.Unmarshal([]byte(aiResponse), &raw); err != nil {
		return nil, fmt.Errorf("invalid JSON: %w", err)
	}

	ctx.CompanyName, _ = raw["company_name"].(string)
	ctx.BusinessModel, _ = raw["business_model"].(string)
	ctx.Notes, _ = raw["notes"].(string)

	ctx.Domains = coerceStringSlice(raw["domains"])
	ctx.Applications = coerceStringSlice(raw["applications"])
	ctx.Teams = coerceStringSlice(raw["teams"])
	ctx.Technologies = coerceStringSlice(raw["technologies"])

	return &ctx, nil
}

func coerceStringSlice(val any) []string {
	arr, ok := val.([]any)
	if !ok {
		return nil
	}
	result := make([]string, 0, len(arr))
	for _, v := range arr {
		if s, ok := v.(string); ok {
			result = append(result, s)
		}
	}
	return result
}
