package engine

import (
	"strings"

	"github.com/precize/common"
	"github.com/precize/contextor/breadcrumb"
	"github.com/precize/contextor/metadata"
)

type ContextEngine struct {
	AI            common.OpenAIClient
	TenantID      string
	CollectedAt   string
	ServiceID     string
	SystemPrompt  string
	EnabledPhases string
}

func (ce *ContextEngine) InitializeGlobalSystemPrompt() error {
	return initializeGlobalSystemPrompt(ce)
}

func (ce *ContextEngine) CollectResourceData(searchAfter any) (map[string]map[string]any, any, error) {
	return collectResourceData(ce, searchAfter)
}

func (ce *ContextEngine) ExtractAndVectorizeBreadcrumbs(resourceDocs []map[string]any) error {
	return breadcrumb.ExtractAndVectorizeBreadcrumbs(resourceDocs, ce.AI.APIKey, ce.SystemPrompt, ce.TenantID)
}

func (ce *ContextEngine) GetSimilarResourcesUsingBreadcrumbs(resourceDocs []map[string]any, bulkContextNeighbourRequest *strings.Builder, contextNeighboursCount *int) error {
	return breadcrumb.GetSimilarResourcesUsingBreadcrumbs(resourceDocs, ce.AI.APIKey, ce.TenantID, bulkContextNeighbourRequest, contextNeighboursCount)
}

func (ce *ContextEngine) VectorizeResourceMetadata(resourceDocs []map[string]any) {
	metadata.VectorizeResourceMetadata(resourceDocs, ce.TenantID)
}

func (ce *ContextEngine) GetSimilarResourceUsingMetadata(resourceDocs []map[string]any, bulkContextNeighbourRequest *strings.Builder, contextNeighboursCount *int) {
	metadata.GetSimilarResourceUsingMetadata(resourceDocs, ce.TenantID, ce.ServiceID, bulkContextNeighbourRequest, contextNeighboursCount)
}
