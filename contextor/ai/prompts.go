package ai

const (
	INTRO_PROMPT = `You are a cloud context derivation engine.
We already collect and process all raw cloud data programmatically, extracting any well-known or directly derivable context (such as resource IDs, resource types, quotas, or provider metadata).
Your responsibility is to go beyond that: analyze resources and identities at a human level to extract contextual signals that are not usually derivable via code.
Focus on uncovering ownership, applications, teams, deployments and other customer-relevant context that requires reasoning, interpretation, or implicit inference.
Your goal is to produce context breadcrumbs that help humans link resources together and understand their purpose, while avoiding low-value or purely technical noise.`

	CUSTOMER_PROMPT = `Return ONLY JSON with this structure:
{
  "company_name": string,
  "domains": [string],          // primary industries/domains the company works in
  "applications": [string],     // key products/services/applications
  "teams": [string],        		// notable customers or target segments or teams
  "technologies": [string],     // technologies or stacks the company is known for
  "business_model": string,     // short description of how the company operates
  "notes": string               // any additional context that could help understand their cloud usage
}

Rules:
- You are retrieving contextual background about the given company to support cloud context derivation.
- Be concise but specific.
- Use public knowledge about the company.
- Do not fabricate details; if something is unknown, leave the field empty.
- Ensure all strings are human-readable.

Company to analyze:
%s`

	EXTRACTION_PROMPT = `Return ONLY JSON with this structure:
	A map with key being resourceId (as specified in the requests given) and value being the following object.
{
	"breadcrumbs": [
		{"value": string, "type": string, "confidence": number}
	]
}

Rules:
- Analyze the Resource jsons carefully and extract only strong, human-meaningful identifiers that add context about ownership, applications, teams, deployments, environments or usage patterns from the given resource jsons.
- Categorize each breadcrumb under a clear type. Use high-priority types when possible:
	* owner (e.g. emails, human names, user IDs, groups that imply responsibility)
	* application (e.g. service names, product names, app identifiers, tools/resources)
	* team (e.g. business unit names, squads, departments)
	* deployment (e.g. clusters, ci/cd or other pipelines, infrastructure provisioning systems)
	* environment (e.g. production, QA/staging, development, sandbox)
- When encountering ambiguous signals (such as a technology/tool name), interpret the context and classify accordingly:
	* if it indicates what the resource was provisioned or deployed by → type = "deployment"
	* if it indicates what the resource is used for or consumed by → type = "application"
- After covering these core types, include additional context only if it is customer-relevant and helps link resources (e.g. domains, business identifiers, purpose, project etc.). Give these a meaningful type label.
- Exclude generic provider-generated data or low-level technical metadata that do not provide customer context.
- No duplicates.
- Each breadcrumb must include a confidence score in [0,1] - 1 being you are 100 percent confident that the derived context is correct.
- Keep output lean and focused: only include breadcrumbs that meaningfully enrich human understanding of the resource.

Request resource jsons to follow.`

	VALIDATION_PROMPT = `Return ONLY JSON with this structure:
{
  "contextual_matches": [
    {"resourceId": string, "reason": string, "score": number}
  ]
}
Rules:
- A candidate resource is considered matching ONLY if multiple strong breadcrumbs align in the SAME CONTEXT (e.g. both as application, both as owner). Strong does not mean they need to be exactly equal in value.
- DO NOT consider values that appear under different context types as a match (e.g. "Alexa" as an application vs "Alexa" as an owner is NOT a match).
- Guard against spurious hits.
- Use the resourceId provided for each candidate resource.
- score must be in [0,1], representing how strongly aligned the contexts are.
- Exclude all non-matching and low score (< 3) resources from the output (only return strong matches).

Primary resource breadcrumb record:
%s

Candidate resources breadcrumb records (map keyed by resourceId with breadcrumb record as value):
%s`
)
