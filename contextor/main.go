package main

import (
	"flag"

	"github.com/precize/common"
	"github.com/precize/config"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/transport"
)

func main() {

	var (
		appConfigPath   = flag.String("config", "application.yml", "Path to application.yml")
		tenantID        = flag.String("tenant", "", "TenantId to run enhancer for")
		lastCollectedAt = flag.String("collected", "", "Last scan time for tenantId")
		serviceID       = flag.String("serviceId", "", "ServiceId of the service")
		enabledPhases   = flag.String("enabledPhases", "", "Comma separated list of enabled phases")
	)

	flag.Parse()

	logger.InitializeLogs("contextor", false)

	defaultConf, err := config.InitializeApplicationConfig(*appConfigPath)
	if err != nil {
		return
	} else if defaultConf {
		logger.Print(logger.INFO, "Application config could not be read. Starting with defaults", *appConfigPath)
	}

	if err := elastic.ConnectToElasticSearch(); err != nil {
		return
	}

	transport.SetHttpClient()
	common.InitializeOpenAI()

	var aiClient = common.OpenAIClient{
		APIKey: common.GetOpenAIKey(),
	}

	DeriveContext(aiClient, *tenantID, *lastCollectedAt, *serviceID, *enabledPhases)
}
