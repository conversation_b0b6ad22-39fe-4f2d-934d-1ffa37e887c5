package main

import (
	"strconv"
	"strings"

	"github.com/precize/common"
	"github.com/precize/contextor/engine"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

const (
	MAX_RECORDS            = 100
	VALIDATION_MAX_RECORDS = 10
)

func DeriveContext(aiClient common.OpenAIClient, tenantID, lastCollectedAt, serviceID, enabledPhases string) error {

	logger.Print(logger.INFO, "Processing context data for tenant", []string{tenantID}, serviceID, lastCollectedAt)

	ce := engine.ContextEngine{
		AI:            aiClient,
		TenantID:      tenantID,
		CollectedAt:   lastCollectedAt,
		ServiceID:     serviceID,
		EnabledPhases: enabledPhases,
	}

	err := ce.InitializeGlobalSystemPrompt()
	if err != nil {
		return err
	}

	Insertion(&ce, tenantID)
	Evaluation(&ce, tenantID)

	logger.Print(logger.INFO, "Processing complete for tenant", []string{tenantID}, serviceID, lastCollectedAt)

	return nil
}

func Insertion(ce *engine.ContextEngine, tenantID string) {

	if len(ce.EnabledPhases) > 0 && !strings.Contains(ce.EnabledPhases, "insertion") {
		return
	}

	var searchAfter any

	logger.Print(logger.INFO, "Insertion started for tenant", []string{tenantID})

	for {

		logger.Print(logger.INFO, "Collecting data", []string{tenantID})

		resourceDocs, sortResponse, err := ce.CollectResourceData(searchAfter)
		if err != nil {
			break
		}

		if len(resourceDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		docs := []map[string]any{}

		for _, resourceDoc := range resourceDocs {

			docs = append(docs, resourceDoc)

			if len(docs) >= MAX_RECORDS {
				ce.VectorizeResourceMetadata(docs)
				ce.ExtractAndVectorizeBreadcrumbs(docs)
				docs = []map[string]any{}
			}
		}

		if len(docs) > 0 {
			ce.VectorizeResourceMetadata(docs)
			ce.ExtractAndVectorizeBreadcrumbs(docs)
		}
	}

	logger.Print(logger.INFO, "Insertion completed for tenant", []string{tenantID})
	return
}

func Evaluation(ce *engine.ContextEngine, tenantID string) {

	if len(ce.EnabledPhases) > 0 && !strings.Contains(ce.EnabledPhases, "evaluation") {
		return
	}

	logger.Print(logger.INFO, "Evaluation started for tenant", []string{tenantID})

	var (
		bulkContextNeighbourRequest strings.Builder
		contextNeighboursCount      int
		searchAfter                 any
	)

	for {

		resourceDocs, sortResponse, err := ce.CollectResourceData(searchAfter)
		if err != nil {
			break
		}

		if len(resourceDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		docs := []map[string]any{}

		for _, resourceDoc := range resourceDocs {

			docs = append(docs, resourceDoc)

			if len(docs) >= MAX_RECORDS {
				ce.GetSimilarResourceUsingMetadata(docs, &bulkContextNeighbourRequest, &contextNeighboursCount)
				ce.GetSimilarResourcesUsingBreadcrumbs(docs, &bulkContextNeighbourRequest, &contextNeighboursCount)
				docs = []map[string]any{}
			}

			if contextNeighboursCount >= VALIDATION_MAX_RECORDS {
				if err := elastic.BulkDocumentsAPI(tenantID, elastic.CONTEXT_NEIGBOURS_INDEX, bulkContextNeighbourRequest.String()); err != nil {
					logger.Print(logger.INFO, "Query failed", []string{tenantID}, contextNeighboursCount, bulkContextNeighbourRequest.String())
					return
				}

				logger.Print(logger.INFO, "Context neighbour bulk API Successful for "+strconv.Itoa(contextNeighboursCount)+" records", []string{tenantID})

				contextNeighboursCount = 0
				bulkContextNeighbourRequest.Reset()
			}
		}

		if len(docs) > 0 {
			ce.GetSimilarResourceUsingMetadata(docs, &bulkContextNeighbourRequest, &contextNeighboursCount)
			ce.GetSimilarResourcesUsingBreadcrumbs(docs, &bulkContextNeighbourRequest, &contextNeighboursCount)
		}

		if contextNeighboursCount > 0 {
			if err := elastic.BulkDocumentsAPI(tenantID, elastic.CONTEXT_NEIGBOURS_INDEX, bulkContextNeighbourRequest.String()); err != nil {
				logger.Print(logger.ERROR, "Query failed", []string{tenantID}, contextNeighboursCount, bulkContextNeighbourRequest.String())
				return
			}

			logger.Print(logger.INFO, "Context neighbour bulk API Successful for "+strconv.Itoa(contextNeighboursCount)+" records", []string{tenantID})

			contextNeighboursCount = 0
			bulkContextNeighbourRequest.Reset()
		}
	}

	logger.Print(logger.INFO, "Evaluation completed for tenant", []string{tenantID})
	return
}
