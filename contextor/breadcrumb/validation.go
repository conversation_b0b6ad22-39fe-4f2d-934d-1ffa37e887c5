package breadcrumb

import (
	"encoding/json"
	"fmt"

	"github.com/precize/common/openai"
	"github.com/precize/contextor/ai"
	"github.com/precize/logger"
)

type ValidateResp struct {
	ContextualMatches []struct {
		ResourceID string  `json:"resourceId"`
		Reason     string  `json:"reason"`
		Score      float64 `json:"score"`
	} `json:"contextual_matches"`
}

func ValidateContext(primaryRecord, candidateRecords, apiKey string) (*ValidateResp, error) {

	convID, err := openai.CreateConversation(apiKey)
	if err != nil {
		return nil, err
	}

	out, err := openai.AskAndRespond(apiKey, convID, "gpt-4o-mini", "user", []string{fmt.Sprintf(ai.VALIDATION_PROMPT, primaryRecord, candidateRecords)}, nil)
	if err != nil {
		return nil, err
	}

	openai.RemoveJSONQuoteFromResponse(&out)

	var validateResp ValidateResp

	if err := json.Unmarshal([]byte(out), &validateResp); err != nil {
		logger.Print(logger.ERROR, "Unmarshal error", err, out)
		return nil, err
	}

	return &validateResp, nil
}
