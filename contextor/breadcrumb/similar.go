package breadcrumb

import (
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/common/basic/uuid"
	resourceutils "github.com/precize/common/resource"
	"github.com/precize/contextor/neighbour"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func GetSimilarResourcesUsingBreadcrumbs(resourceDocs []map[string]any, apiKey, tenantID string, bulkContextNeighbourRequest *strings.Builder, contextNeighboursCount *int) error {

	for _, resourceDoc := range resourceDocs {

		rscDocBytes, err := json.Marshal(resourceDoc)
		if err != nil {
			logger.Print(logger.ERROR, "Error marshalling alert data to JSON", []string{tenantID}, err)
			continue
		}

		var crDoc resourceutils.CloudResource
		if err = json.Unmarshal(rscDocBytes, &crDoc); err != nil {
			logger.Print(logger.ERROR, "Error unmarshalling JSON", []string{tenantID}, err)
			continue
		}

		logger.Print(logger.INFO, "Getting breadcrumbs of entityId", []string{tenantID}, crDoc.EntityID)

		primaryRecordID := uuid.GenerateUUIDFromString(crDoc.EntityID + crDoc.EntityType + tenantID)

		primaryRecord, err := GetBreadcrumbByID(primaryRecordID)
		if err != nil {
			continue
		}

		logger.Print(logger.INFO, "Got breadcrumbs", []string{tenantID}, *primaryRecord, crDoc.EntityID)

		if len(primaryRecord.EntityID) > 0 {

			candidateRecords, err := GetSimilarBreadcrumbs(primaryRecord.Breadcrumbs, tenantID, strconv.Itoa(crDoc.ServiceID), primaryRecordID)
			if err != nil {
				continue
			}

			// var candidatesBreadcrumbMap = make(map[string]string)

			// for _, candidateRecord := range candidateRecords {
			// score, err := strconv.ParseFloat(candidateRecord.Additional.Score, 64)
			// if err == nil && score < 0.5 {
			// 	continue
			// }

			// candidatesBreadcrumbMap[candidateRecord.EntityID] = candidateRecord.Breadcrumbs
			// }

			// candidatesBreadcrumbs, err := json.Marshal(candidatesBreadcrumbMap)
			// if err != nil {
			// 	logger.Print(logger.ERROR, "Marshal error", err)
			// 	continue
			// }

			// validateResp, err := ValidateContext(primaryRecord.Breadcrumbs, string(candidatesBreadcrumbs), apiKey)
			// if err != nil {
			// 	continue
			// }

			logger.Print(logger.INFO, "Final", candidateRecords, crDoc.EntityID)

			for _, candidateRecord := range candidateRecords {

				breadcrumbNeighbourType := "breadcrumbs"

				docID := common.GenerateCombinedHashID(primaryRecord.EntityID, candidateRecord.EntityID, breadcrumbNeighbourType, tenantID)

				score := NormalizeVectorDistanceToScore(candidateRecord.AdditionalInfo.Distance)

				contextNeighbourDoc := neighbour.ContextNeighboursDoc{
					Type: breadcrumbNeighbourType,
					ResourceA: neighbour.NeighbourResource{
						EntityID:   primaryRecord.EntityID,
						EntityType: primaryRecord.EntityType,
						Value:      primaryRecord.Breadcrumbs,
					},
					ResourceB: neighbour.NeighbourResource{
						EntityID:   candidateRecord.EntityID,
						EntityType: candidateRecord.EntityType,
						Value:      candidateRecord.Breadcrumbs,
					},
					Score:      float32(score),
					TenantID:   tenantID,
					ServiceID:  crDoc.ServiceID,
					ID:         docID,
					Deleted:    false,
					InsertTime: elastic.DateTime(time.Now()),
				}

				contextNeighbourInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
				contextNeighbourInsertDoc, err := json.Marshal(contextNeighbourDoc)
				if err != nil {
					logger.Print(logger.ERROR, "Got error marshalling document", err)
					continue
				}

				bulkContextNeighbourRequest.WriteString(contextNeighbourInsertMetadata)
				bulkContextNeighbourRequest.WriteString("\n")
				bulkContextNeighbourRequest.Write(contextNeighbourInsertDoc)
				bulkContextNeighbourRequest.WriteString("\n")

				*contextNeighboursCount++
			}
		}
	}

	return nil
}
