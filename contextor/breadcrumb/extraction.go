package breadcrumb

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/precize/common/basic/uuid"

	"github.com/precize/common"
	cloudutils "github.com/precize/common/cloud"
	"github.com/precize/common/openai"
	resourceutils "github.com/precize/common/resource"
	"github.com/precize/contextor/ai"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

type Breadcrumb struct {
	Value      string  `json:"value"`
	Type       string  `json:"type,omitempty"`
	Confidence float64 `json:"confidence,omitempty"`
}

type extractionRequest struct {
	ResourceName string                `json:"resourceName"`
	Description  string                `json:"description"`
	Tags         []resourceutils.CRTag `json:"tags"`

	crDoc resourceutils.CloudResource `json:"-"`
}

func ExtractAndVectorizeBreadcrumbs(resourceDocs []map[string]any, apiKey, systemPrompt, tenantID string) error {

	var (
		bcRecords                                    []BreadcrumbRecord
		extractionBulkRequest                        = make(map[string]extractionRequest)
		extractionRecordCount, textLookupRecordCount int
		bulkTextLookupRequest                        strings.Builder
	)

	for _, resourceDoc := range resourceDocs {

		rscDocBytes, err := json.Marshal(resourceDoc)
		if err != nil {
			logger.Print(logger.ERROR, "Error marshalling alert data to JSON", []string{tenantID}, err)
			continue
		}

		var crDoc resourceutils.CloudResource
		if err = json.Unmarshal(rscDocBytes, &crDoc); err != nil {
			logger.Print(logger.ERROR, "Error unmarshalling JSON", []string{tenantID}, err)
			continue
		}

		extractionReq, err := createExtractionRequest(crDoc)
		if err != nil {
			continue
		}

		extractionReqJSON, err := json.Marshal(*extractionReq)
		if err != nil {
			logger.Print(logger.ERROR, "Marshal error", err)
			continue
		}

		textHash := common.HashSha256(extractionReqJSON)
		textLookupDocID := common.GenerateCombinedHashID(textHash)

		if doc, _ := elastic.GetDocument(elastic.TEXT_LOOKUP_INDEX, textLookupDocID); len(doc) > 0 {
			if dataString, ok := doc["data"].(string); ok {
				var breadcrumbs []Breadcrumb
				if err := json.Unmarshal([]byte(dataString), &breadcrumbs); err != nil {
					logger.Print(logger.ERROR, "Unmarshal error", err)
					continue
				}

				if err = stageBreadcrumbRecord(crDoc, breadcrumbs, &bcRecords); err != nil {
					continue
				}
			}
		} else {

			logger.Print(logger.INFO, "Extracting data", []string{tenantID}, crDoc.EntityID, *extractionReq)

			extractionRecordCount++
			extractionBulkRequest[crDoc.EntityID] = *extractionReq

			if extractionRecordCount >= 20 {

				if err = extractBreadcrumbs(extractionBulkRequest, systemPrompt, apiKey, tenantID, &bulkTextLookupRequest, &textLookupRecordCount, &bcRecords); err != nil {
					continue
				}

				extractionRecordCount = 0
				extractionBulkRequest = make(map[string]extractionRequest)
			}
		}
	}

	if extractionRecordCount > 0 {
		err := extractBreadcrumbs(extractionBulkRequest, systemPrompt, apiKey, tenantID, &bulkTextLookupRequest, &textLookupRecordCount, &bcRecords)
		if err != nil {
			return err
		}
	}

	logger.Print(logger.INFO, "Inserting breadcrumbs", []string{tenantID}, bcRecords)

	if len(bcRecords) > 0 {
		if err := InsertBreadcrumbs(bcRecords); err != nil {
			return err
		}
	}

	if textLookupRecordCount > 0 {
		if err := elastic.BulkDocumentsAPI(tenantID, elastic.TEXT_LOOKUP_INDEX, bulkTextLookupRequest.String()); err != nil {
			logger.Print(logger.ERROR, "Query failed", []string{tenantID}, textLookupRecordCount, bulkTextLookupRequest.String())
			return err
		}

		logger.Print(logger.INFO, "Text lookup bulk API Successful for "+strconv.Itoa(textLookupRecordCount)+" records", []string{tenantID})

		bulkTextLookupRequest.Reset()
	}

	return nil
}

func createExtractionRequest(crDoc resourceutils.CloudResource) (*extractionRequest, error) {

	var (
		extractionReq        = &extractionRequest{}
		entityJSONMap        = make(map[string]any)
		resourceNameTagValue string
	)

	err := json.Unmarshal([]byte(crDoc.EntityJson), &entityJSONMap)
	if err != nil {
		logger.Print(logger.ERROR, "Unmarshal error", err)
		return nil, err
	}

	extractionReq.Description, _ = entityJSONMap["description"].(string)

	for _, tag := range crDoc.Tags {
		if tag.Key == "Name" {
			resourceNameTagValue = tag.Value
		}

		extractionReq.Tags = append(extractionReq.Tags, resourceutils.CRTag{Key: tag.Key, Value: tag.Value})
	}

	switch crDoc.ServiceID {
	case common.AWS_SERVICE_ID_INT:
		extractionReq.ResourceName = cloudutils.GetNameForAWSResource(crDoc.EntityID, crDoc.EntityType, resourceNameTagValue, crDoc.EntityJson)
	case common.AZURE_SERVICE_ID_INT:
		extractionReq.ResourceName = cloudutils.GetNameForAzureResource(crDoc.EntityID, crDoc.EntityType, crDoc.EntityJson)
	case common.GCP_SERVICE_ID_INT:
		extractionReq.ResourceName = cloudutils.GetNameForGCPResource(crDoc.EntityID, crDoc.EntityType, crDoc.EntityJson)
	case common.OPENAI_SERVICE_ID_INT:
		extractionReq.ResourceName = cloudutils.GetNameForOpenAIResource(crDoc.EntityID, crDoc.EntityJson)
	default:
		if name := cloudutils.GetResourceNameFromEntityJSON(crDoc.EntityJson); len(name) > 0 {
			extractionReq.ResourceName = name
		}
	}

	extractionReq.crDoc = crDoc

	return extractionReq, err
}

func extractBreadcrumbs(extractionBulkRequest map[string]extractionRequest, systemPrompt, apiKey, tenantID string, bulkTextLookupRequest *strings.Builder, textLookupRecordCount *int, bcRecords *[]BreadcrumbRecord) error {

	extractionBulkRequestJSON, err := json.Marshal(extractionBulkRequest)
	if err != nil {
		logger.Print(logger.ERROR, "Marshal error", err)
		return err
	}

	convID, err := openai.CreateConversation(apiKey)
	if err != nil {
		return err
	}

	err = openai.Ask(apiKey, convID, "gpt-4o-mini", "system", []string{systemPrompt, ai.EXTRACTION_PROMPT}, nil)
	if err != nil {
		return err
	}

	out, err := openai.AskAndRespond(apiKey, convID, "gpt-4o-mini", "user", []string{fmt.Sprintf(string(extractionBulkRequestJSON))}, nil)
	if err != nil {
		return err
	}

	openai.RemoveJSONQuoteFromResponse(&out)

	if err := openai.DeleteConversation(apiKey, convID); err != nil {
		return err
	}

	// EntityId -> Breadcrumbs object
	var bcResps = make(map[string]struct {
		Breadcrumbs []Breadcrumb `json:"breadcrumbs"`
	})

	if err := json.Unmarshal([]byte(out), &bcResps); err != nil {
		logger.Print(logger.ERROR, "Unmarshal error", err, out)
		return err
	}

	logger.Print(logger.INFO, "Extraction response", []string{tenantID}, bcResps)

	for entityID, bcResp := range bcResps {

		err = stageBreadcrumbRecord(extractionBulkRequest[entityID].crDoc, bcResp.Breadcrumbs, bcRecords)
		if err != nil {
			continue
		}

		breadcrumbsJSON, err := json.Marshal(bcResp.Breadcrumbs)
		if err != nil {
			logger.Print(logger.ERROR, "Marshal error", err)
			continue
		}

		extractionReqJSON, err := json.Marshal(extractionBulkRequest[entityID])
		if err != nil {
			logger.Print(logger.ERROR, "Marshal error", err)
			continue
		}

		textHash := common.HashSha256(extractionReqJSON)
		textLookupDocID := common.GenerateCombinedHashID(textHash)

		textLookupDoc := common.TextLookupInsertDoc{
			Text:       textHash,
			Data:       string(breadcrumbsJSON),
			TenantID:   tenantID,
			InsertTime: elastic.DateTime(time.Now().UTC()),
			Category:   "Breadcrumb Extraction",
		}

		textLookupInsertMetadata := `{"index": {"_id": "` + textLookupDocID + `"}}`
		textLookupInsertDoc, err := json.Marshal(textLookupDoc)
		if err != nil {
			logger.Print(logger.ERROR, "Got error marshalling document", err)
			continue
		}

		bulkTextLookupRequest.WriteString(textLookupInsertMetadata)
		bulkTextLookupRequest.WriteString("\n")
		bulkTextLookupRequest.Write(textLookupInsertDoc)
		bulkTextLookupRequest.WriteString("\n")

		*textLookupRecordCount++
	}

	return nil
}

func stageBreadcrumbRecord(crDoc resourceutils.CloudResource, breadcrumbs []Breadcrumb, bcRecords *[]BreadcrumbRecord) error {

	var breadcrumbMap = make(map[string][]string)

	for _, breadcrumb := range breadcrumbs {
		breadcrumbMap[breadcrumb.Type] = append(breadcrumbMap[breadcrumb.Type], breadcrumb.Value)
	}

	bcBytes, err := json.Marshal(breadcrumbMap)
	if err != nil {
		logger.Print(logger.ERROR, "Marshal error", err)
		return err
	}

	id := uuid.GenerateUUIDFromString(crDoc.EntityID + crDoc.EntityType + crDoc.TenantID)

	bcRecord := BreadcrumbRecord{
		EntityID:    crDoc.EntityID,
		EntityType:  crDoc.EntityType,
		Breadcrumbs: string(bcBytes),
		TenantID:    crDoc.TenantID,
		ServiceID:   crDoc.ServiceID,
		Source:      "resource_properties",
		ID:          id,
	}

	*bcRecords = append(*bcRecords, bcRecord)
	return nil
}
