package breadcrumb

import (
	"encoding/json"

	"github.com/precize/common/weaviate"
	"github.com/precize/logger"
)

const BREADCRUMB_CLASS = "Breadcrumbs"

type BreadcrumbRecord struct {
	ID          string `json:"id"`
	EntityID    string `json:"entityId,omitempty"`
	EntityType  string `json:"entityType,omitempty"`
	Breadcrumbs string `json:"breadcrumbs"`
	TenantID    string `json:"tenantId,omitempty"`
	ServiceID   int    `json:"serviceId,omitempty"`
	Source      string `json:"source,omitempty"`
}

type BreadcrumbRecordSearch struct {
	ID             string         `json:"id"`
	EntityID       string         `json:"entityId,omitempty"`
	EntityType     string         `json:"entityType,omitempty"`
	Breadcrumbs    string         `json:"breadcrumbs"`
	TenantID       string         `json:"tenantId,omitempty"`
	ServiceID      int            `json:"serviceId,omitempty"`
	Source         string         `json:"source,omitempty"`
	AdditionalInfo AdditionalInfo `json:"_additional"`
}

type AdditionalInfo struct {
	Distance float64 `json:"distance"`
}

type BreadcrumbSearchResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    struct {
		Data struct {
			Get struct {
				Breadcrumbs []BreadcrumbRecord `json:"Breadcrumbs"`
			} `json:"Get"`
		} `json:"data"`
	} `json:"data"`
}

type BreadcrumbSimilarSearchResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    struct {
		Data struct {
			Get struct {
				Breadcrumbs []BreadcrumbRecordSearch `json:"Breadcrumbs"`
			} `json:"Get"`
		} `json:"data"`
	} `json:"data"`
}

type BreadcrumbGetResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    []struct {
		Class              string           `json:"class"`
		CreationTimeUnix   int64            `json:"creationTimeUnix"`
		ID                 string           `json:"id"`
		LastUpdateTimeUnix int64            `json:"lastUpdateTimeUnix"`
		Properties         BreadcrumbRecord `json:"properties"`
		// Skipping "vectors"
	} `json:"data"`
}

func InsertBreadcrumbs(records []BreadcrumbRecord) error {
	return weaviate.InsertWithID(BREADCRUMB_CLASS, records)
}

func GetBreadcrumbByID(id string) (*BreadcrumbRecord, error) {

	resp, err := weaviate.SearchByID(BREADCRUMB_CLASS, id)
	if err != nil {
		return nil, err
	}

	var bcSearchResp BreadcrumbGetResponse
	if err := json.Unmarshal(resp, &bcSearchResp); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal Breadcrumbs id search response", err)
		return nil, err
	}

	if !bcSearchResp.Success {
		logger.Print(logger.INFO, "Record with id does not exist", id)
		return nil, nil
	}

	if len(bcSearchResp.Data) > 0 {
		logger.Print(logger.INFO, "Weaviate Breadcrumbs id search successful", bcSearchResp.Message)
		return &bcSearchResp.Data[0].Properties, nil
	}

	return nil, nil
}

func GetSimilarBreadcrumbs(query, tenantID, serviceID, id string) ([]BreadcrumbRecordSearch, error) {

	filters := `{operator:And,operands:[
						{operator:Equal,path:["tenantId"],valueText:"` + tenantID + `"},
						{operator:Equal,path:["serviceId"],valueNumber:` + serviceID + `},
						{operator:NotEqual,path:["id"],valueText:"` + id + `"}
					]}`

	resp, err := weaviate.SearchSimilar(BREADCRUMB_CLASS, query, "vector", "", filters, []string{"entityId", "breadcrumbs", "entityType", "_additional { score id }"}, 1, 0.5, 10)
	if err != nil {
		return nil, err
	}

	var bcSearchResp BreadcrumbSimilarSearchResponse
	if err := json.Unmarshal(resp, &bcSearchResp); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal Breadcrumbs search response", err)
		return nil, err
	}

	if !bcSearchResp.Success {
		return nil, nil
	}

	logger.Print(logger.INFO, "Weaviate Breadcrumbs similar search successful for "+query)
	return bcSearchResp.Data.Data.Get.Breadcrumbs, nil
}

// Replaced by GetBreadcrumbByID now
func GetBreadcrumbByEntityID(entityID, tenantID, serviceID string) (*BreadcrumbRecord, error) {

	filters := `{operator:And,operands:[
						{operator:Equal,path:["entityId"],valueText:"` + entityID + `"},
						{operator:Equal,path:["tenantId"],valueText:"` + tenantID + `"},
						{operator:Equal,path:["serviceId"],valueNumber:` + serviceID + `}
					]}`

	resp, err := weaviate.SearchByQuery(BREADCRUMB_CLASS, filters, []string{"entityId", "breadcrumbs", "_additional { score id }"}, 1)
	if err != nil {
		return nil, err
	}

	var bcSearchResp BreadcrumbSearchResponse
	if err := json.Unmarshal(resp, &bcSearchResp); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal Breadcrumbs query response", err)
		return nil, err
	}

	if !bcSearchResp.Success {
		logger.Print(logger.INFO, "Record with entityId does not exist", entityID)
		return nil, nil
	}

	if len(bcSearchResp.Data.Data.Get.Breadcrumbs) > 0 {
		logger.Print(logger.INFO, "Weaviate Breadcrumbs search by entityId successful", bcSearchResp.Message)
		return &bcSearchResp.Data.Data.Get.Breadcrumbs[0], nil
	}

	return nil, nil
}
