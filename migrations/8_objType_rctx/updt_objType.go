package main

import (
	"encoding/json"
	"slices"
	"strings"
	"sync"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/enhancer/context"
	"github.com/precize/logger"
	"github.com/precize/provider/wiz/types"
)

type PrimaryContext struct {
	Owners      []string
	Env         []string
	App         []string
	Team        []string
	Software    []string
	Deployment  []string
	Sensitivity []string
	Compliance  []string
	CostCenter  []string
	TTL         []string
}

const (
	MAX_THREADS = 10
)

func UpdateObjTypeForRctx() {
	rctxAggsQuery := `{"query":{"match_all":{}},"from":0,"size":0,"sort":[],"aggs":{"tenant":{"terms":{"field":"tenantId.keyword","size":10000},"aggs":{"lastCollected":{"terms":{"field":"lastCollectedAt.keyword","size":10000}}}}}}`
	rctxAggsDocs, err := elastic.ExecuteSearchForAggregation([]string{elastic.RESOURCE_CONTEXT_INDEX}, rctxAggsQuery)
	if err != nil {
		return
	}

	jobs := make(chan [2]string, 100)
	var wg sync.WaitGroup

	for i := 0; i < MAX_THREADS; i++ {
		go func() {
			for job := range jobs {
				UpdateObjTypeForRctxForTenantAndCollectedAt(job[0], job[1])
				wg.Done()
			}
		}()
	}

	for _, tenant := range rctxAggsDocs["tenant"].(map[string]any)["buckets"].([]any) {
		tenantID := tenant.(map[string]any)["key"].(string)
		for _, lastCollected := range tenant.(map[string]any)["lastCollected"].(map[string]any)["buckets"].([]any) {
			lastCollectedAt := lastCollected.(map[string]any)["key"].(string)
			wg.Add(1)
			jobs <- [2]string{tenantID, lastCollectedAt}
		}
	}

	close(jobs)
	wg.Wait()
}

func UpdateObjTypeForRctxForTenantAndCollectedAt(tenantID, lastCollectedAt string) {

	if len(tenantID) <= 0 || len(lastCollectedAt) <= 0 {
		return
	}

	logger.Print(logger.INFO, "Processing started for tenant and collectedAt", []string{tenantID, lastCollectedAt})

	resourcesQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"collectedAt":"` + lastCollectedAt + `"}}]}}}`
	var (
		searchAfter                any
		bulkResourceContextRequest strings.Builder
	)

	for {

		resourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, resourcesQuery, searchAfter)
		if err != nil {
			return
		}

		if len(resourcesDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		logger.Print(logger.INFO, "Resources fetched", len(resourcesDocs))

		var (
			collectedCRDocIDs   []string
			collectedRctxDocIDs []string
			primaryContext      = make(map[string]PrimaryContext)
			rctxToCR            = make(map[string]string)
		)

		for resourcesDocID, crDoc := range resourcesDocs {
			collectedCRDocIDs = append(collectedCRDocIDs, resourcesDocID)

			crDocBytes, err := json.Marshal(crDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Error marshalling alert data to JSON", []string{tenantID}, err)
				continue
			}

			var crDoc types.CloudResource
			if err = json.Unmarshal(crDocBytes, &crDoc); err != nil {
				logger.Print(logger.ERROR, "Error unmarshalling JSON", []string{tenantID}, err)
				continue
			}

			if slices.Contains([]string{common.AZURE_LOCATION_RESOURCE_TYPE, common.AZURE_ASSIGNEDROLE_RESOURCE_TYPE, common.AZURE_GROUPS_RESOURCE_TYPE, common.AZURE_POLICYSTATE_RESOURCE_TYPE, common.AZURE_POLICYDEFINITION_RESOURCE_TYPE, common.AWS_REGION_RESOURCE_TYPE, common.GCP_REGION_RESOURCE_TYPE, common.GCP_CONSTRAINT_RESOURCE_TYPE, common.APP_RESOURCE_TYPE}, crDoc.EntityType) {
				continue
			}

			primaryContext[resourcesDocID] = PrimaryContext{
				Owners:      crDoc.Owner,
				Env:         crDoc.Environment,
				App:         crDoc.App,
				Team:        crDoc.Team,
				Software:    crDoc.Software,
				Deployment:  crDoc.Deployment,
				Sensitivity: crDoc.Sensitivity,
				Compliance:  crDoc.Compliance,
				CostCenter:  crDoc.CostCenter,
				TTL:         crDoc.TTL,
			}

			contextDocID := ""
			switch crDoc.EntityType {
			case common.AWS_ORG_RESOURCE_TYPE, common.AWS_ORGUNIT_RESOURCE_TYPE, common.AWS_ACCOUNT_RESOURCE_TYPE, common.AZURE_SUBSCRIPTION_RESOURCE_TYPE, common.AZURE_RG_RESOURCE_TYPE, common.GCP_FOLDER_RESOURCE_TYPE, common.GCP_PROJECT_RESOURCE_TYPE:
				contextDocID = common.GenerateCombinedHashID(crDoc.EntityID, crDoc.EntityType, crDoc.EntityID, lastCollectedAt, tenantID)
			default:
				contextDocID = common.GenerateCombinedHashID(crDoc.EntityID, crDoc.EntityType, crDoc.AccountID, lastCollectedAt, tenantID)
			}

			collectedRctxDocIDs = append(collectedRctxDocIDs, contextDocID)
			rctxToCR[contextDocID] = resourcesDocID
		}

		var (
			searchAfter any
		)

		for {
			rctxQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"lastCollectedAt":"` + lastCollectedAt + `"}},{"terms":{"id.keyword":["` + strings.Join(collectedRctxDocIDs, `","`) + `"]}}]}}}`
			rctxDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.RESOURCE_CONTEXT_INDEX}, rctxQuery, searchAfter)
			if err != nil {
				return
			}

			if len(rctxDocs) > 0 {
				searchAfter = sortResponse
			} else {
				break
			}

			logger.Print(logger.INFO, "Resource context fetched", len(rctxDocs))

			for docID, rctxDoc := range rctxDocs {

				if i := slices.Index(collectedRctxDocIDs, docID); i != -1 {
					collectedRctxDocIDs = slices.Delete(collectedRctxDocIDs, i, i+1)
				}

				var rctx common.ResourceContextInsertDoc
				jsonBytes, er := json.Marshal(rctxDoc)
				if er != nil {
					continue
				}
				err = json.Unmarshal(jsonBytes, &rctx)
				if err != nil {
					continue
				}

				crDocID := common.GenerateCombinedHashIDCaseSensitive(rctx.TenantID, rctx.LastCollectedAt, strings.ToLower(rctx.Account), strings.ToLower(rctx.ResourceID), rctx.ResourceType)
				if primaryContext, ok := primaryContext[crDocID]; ok {
					UpdateObjTypeForRctxForDoc(&rctx, primaryContext)
				} else {
					logger.Print(logger.INFO, "Primary context not found", crDocID)
					continue
				}

				resourceContextInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
				resourceContextInsertDoc, err := json.Marshal(rctx)
				if err != nil {
					logger.Print(logger.ERROR, "Got error marshalling document", err)
					return
				}

				bulkResourceContextRequest.WriteString(resourceContextInsertMetadata)
				bulkResourceContextRequest.WriteString("\n")
				bulkResourceContextRequest.Write(resourceContextInsertDoc)
				bulkResourceContextRequest.WriteString("\n")
			}
		}

		if len(collectedRctxDocIDs) > 0 {
			logger.Print(logger.INFO, "Resource context not found", len(collectedRctxDocIDs))

			crIDs := make([]string, 0)
			for _, rctx := range collectedRctxDocIDs {
				crIDs = append(crIDs, rctxToCR[rctx])
			}

			var (
				collectedCRDocIDs   []string
				collectedRctxDocIDs []string
				primaryContext      = make(map[string]PrimaryContext)
			)

			crQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"terms":{"id.keyword":["` + strings.Join(crIDs, `","`) + `"]}}]}}}`

			resourcesDocs, err := elastic.ExecuteSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, crQuery)
			if err != nil {
				return
			}

			for _, crDoc := range resourcesDocs {

				if resourcesDocID, ok := crDoc["id"].(string); ok {

					collectedCRDocIDs = append(collectedCRDocIDs, resourcesDocID)

					crDocBytes, err := json.Marshal(crDoc)
					if err != nil {
						logger.Print(logger.ERROR, "Error marshalling alert data to JSON", []string{tenantID}, err)
						continue
					}

					var crDoc types.CloudResource
					if err = json.Unmarshal(crDocBytes, &crDoc); err != nil {
						logger.Print(logger.ERROR, "Error unmarshalling JSON", []string{tenantID}, err)
						continue
					}

					primaryContext[resourcesDocID] = PrimaryContext{
						Owners:      crDoc.Owner,
						Env:         crDoc.Environment,
						App:         crDoc.App,
						Team:        crDoc.Team,
						Software:    crDoc.Software,
						Deployment:  crDoc.Deployment,
						Sensitivity: crDoc.Sensitivity,
						Compliance:  crDoc.Compliance,
						CostCenter:  crDoc.CostCenter,
						TTL:         crDoc.TTL,
					}

					// without account ID
					contextDocID := common.GenerateCombinedHashID(crDoc.EntityID, crDoc.EntityType, crDoc.AccountID, tenantID)
					collectedRctxDocIDs = append(collectedRctxDocIDs, contextDocID)
				}
			}

			var (
				searchAfter any
			)

			for {

				rctxQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"lastCollectedAt":"` + lastCollectedAt + `"}},{"terms":{"id.keyword":["` + strings.Join(collectedRctxDocIDs, `","`) + `"]}}]}}}`
				rctxDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.RESOURCE_CONTEXT_INDEX}, rctxQuery, searchAfter)
				if err != nil {
					return
				}

				if len(rctxDocs) > 0 {
					searchAfter = sortResponse
				} else {
					break
				}

				logger.Print(logger.INFO, "Resource context fetched", len(rctxDocs))

				for docID, rctxDoc := range rctxDocs {

					collectedRctxDocIDs = slices.DeleteFunc(collectedRctxDocIDs, func(id string) bool {
						return id == docID
					})

					var rctx common.ResourceContextInsertDoc
					jsonBytes, er := json.Marshal(rctxDoc)
					if er != nil {
						continue
					}
					err = json.Unmarshal(jsonBytes, &rctx)
					if err != nil {
						continue
					}

					crDocID := common.GenerateCombinedHashIDCaseSensitive(rctx.TenantID, rctx.LastCollectedAt, strings.ToLower(rctx.Account), strings.ToLower(rctx.ResourceID), rctx.ResourceType)
					if primaryContext, ok := primaryContext[crDocID]; ok {
						UpdateObjTypeForRctxForDoc(&rctx, primaryContext)
					} else {
						logger.Print(logger.INFO, "Primary context not found", crDocID)
						continue
					}

					resourceContextInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
					resourceContextInsertDoc, err := json.Marshal(rctx)
					if err != nil {
						logger.Print(logger.ERROR, "Got error marshalling document", err)
						return
					}

					bulkResourceContextRequest.WriteString(resourceContextInsertMetadata)
					bulkResourceContextRequest.WriteString("\n")
					bulkResourceContextRequest.Write(resourceContextInsertDoc)
					bulkResourceContextRequest.WriteString("\n")
				}
			}
		}

		if len(bulkResourceContextRequest.String()) > 0 {
			if err := elastic.BulkDocumentsAPI(tenantID, elastic.RESOURCE_CONTEXT_INDEX, bulkResourceContextRequest.String()); err != nil {
				return
			}
		}

		logger.Print(logger.INFO, "Resource context bulk API Successful", []string{tenantID})
		bulkResourceContextRequest.Reset()
	}

	time.Sleep(500 * time.Millisecond)
	logger.Print(logger.INFO, "Processing completed for tenant and collectedAt", []string{tenantID, lastCollectedAt})

}

func UpdateObjTypeForRctxForDoc(rctx *common.ResourceContextInsertDoc, primaryContext PrimaryContext) {

	for _, owner := range primaryContext.Owners {

		if owner == "NONE" {
			continue
		}

		isOwnerFound := false
		for i, rctxOwner := range rctx.ResourceOwnerTypes.DefinedOwners {
			if strings.ToLower(rctxOwner.Name) == strings.ToLower(owner) {
				rctx.ResourceOwnerTypes.DefinedOwners[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
				isOwnerFound = true
				break
			}
		}

		if !isOwnerFound {
			for i, rctxOwner := range rctx.ResourceOwnerTypes.DerivedOwners {
				if strings.ToLower(rctxOwner.Name) == strings.ToLower(owner) {
					rctx.ResourceOwnerTypes.DerivedOwners[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
					isOwnerFound = true
					break
				}
			}
		}

		if !isOwnerFound {
			for i, rctxOwner := range rctx.ResourceOwnerTypes.CodeOwners {
				if strings.ToLower(rctxOwner.Name) == strings.ToLower(owner) {
					rctx.ResourceOwnerTypes.CodeOwners[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
					isOwnerFound = true
					break
				}
			}
		}

		if !isOwnerFound {
			for i, rctxOwner := range rctx.ResourceOwnerTypes.OpsOwners {
				if strings.ToLower(rctxOwner.Name) == strings.ToLower(owner) {
					rctx.ResourceOwnerTypes.OpsOwners[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
					isOwnerFound = true
					break
				}
			}
		}

		if !isOwnerFound {
			for i, rctxOwner := range rctx.ResourceOwnerTypes.InheritedOwners {
				if strings.ToLower(rctxOwner.Name) == strings.ToLower(owner) {
					rctx.ResourceOwnerTypes.InheritedOwners[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
					isOwnerFound = true
					break
				}
			}
		}
	}

	for _, env := range primaryContext.Env {

		isEnvFound := false
		for i, rctxEnv := range rctx.ResourceEnvTypes.DefinedEnv {
			if strings.ToLower(rctxEnv.Name) == strings.ToLower(env) && !isEnvFound {
				rctx.ResourceEnvTypes.DefinedEnv[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
				isEnvFound = true
			}

			if len(rctxEnv.Desc) == 0 {
				rctx.ResourceEnvTypes.DefinedEnv[i].Desc = context.GetStaticDescriptionOfEnvType(rctxEnv.Type)
			}
		}

		for i, rctxEnv := range rctx.ResourceEnvTypes.DerivedEnv {
			if strings.ToLower(rctxEnv.Name) == strings.ToLower(env) && !isEnvFound {
				rctx.ResourceEnvTypes.DerivedEnv[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
				isEnvFound = true
			}

			if len(rctxEnv.Desc) == 0 {
				rctx.ResourceEnvTypes.DerivedEnv[i].Desc = context.GetStaticDescriptionOfEnvType(rctxEnv.Type)
			}
		}

		for i, rctxEnv := range rctx.ResourceEnvTypes.InheritedEnv {
			if strings.ToLower(rctxEnv.Name) == strings.ToLower(env) && !isEnvFound {
				rctx.ResourceEnvTypes.InheritedEnv[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
				isEnvFound = true
			}

			if len(rctxEnv.Desc) == 0 {
				rctx.ResourceEnvTypes.InheritedEnv[i].Desc = context.GetStaticDescriptionOfEnvType(rctxEnv.Type)
			}
		}
	}

	for _, app := range primaryContext.App {

		isAppFound := false
		for i, rctxApp := range rctx.ResourceAppTypes.DefinedApp {
			if strings.ToLower(rctxApp.Name) == strings.ToLower(app) && !isAppFound {
				rctx.ResourceAppTypes.DefinedApp[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
				isAppFound = true
			}

			if len(rctxApp.Desc) == 0 {
				rctx.ResourceAppTypes.DefinedApp[i].Desc = context.GetStaticDescriptionOfAppType(rctxApp.Type)
			}
		}

		for i, rctxApp := range rctx.ResourceAppTypes.DerivedApp {
			if strings.ToLower(rctxApp.Name) == strings.ToLower(app) && !isAppFound {
				rctx.ResourceAppTypes.DerivedApp[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
				isAppFound = true
			}

			if len(rctxApp.Desc) == 0 {
				rctx.ResourceAppTypes.DerivedApp[i].Desc = context.GetStaticDescriptionOfAppType(rctxApp.Type)
			}
		}
	}

	for _, team := range primaryContext.Team {

		isTeamFound := false
		for i, rctxTeam := range rctx.ResourceTeamTypes.DefinedTeam {
			if strings.ToLower(rctxTeam.Name) == strings.ToLower(team) && !isTeamFound {
				rctx.ResourceTeamTypes.DefinedTeam[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
				isTeamFound = true
			}

			if len(rctxTeam.Desc) == 0 {
				rctx.ResourceTeamTypes.DefinedTeam[i].Desc = context.GetStaticDescriptionOfTeamType(rctxTeam.Type)
			}
		}

		for i, rctxTeam := range rctx.ResourceTeamTypes.DerivedTeam {
			if strings.ToLower(rctxTeam.Name) == strings.ToLower(team) && !isTeamFound {
				rctx.ResourceTeamTypes.DerivedTeam[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
				isTeamFound = true
			}

			if len(rctxTeam.Desc) == 0 {
				rctx.ResourceTeamTypes.DerivedTeam[i].Desc = context.GetStaticDescriptionOfTeamType(rctxTeam.Type)
			}
		}

		for i, rctxTeam := range rctx.ResourceTeamTypes.InheritedTeam {
			if strings.ToLower(rctxTeam.Name) == strings.ToLower(team) && !isTeamFound {
				rctx.ResourceTeamTypes.InheritedTeam[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
				isTeamFound = true
			}

			if len(rctxTeam.Desc) == 0 {
				rctx.ResourceTeamTypes.InheritedTeam[i].Desc = context.GetStaticDescriptionOfTeamType(rctxTeam.Type)
			}
		}
	}

	for _, software := range primaryContext.Software {

		isSoftwareFound := false
		for i, rctxSoftware := range rctx.ResourceSoftwareTypes.DefinedSoftware {
			if strings.ToLower(rctxSoftware.Name) == strings.ToLower(software) && !isSoftwareFound {
				rctx.ResourceSoftwareTypes.DefinedSoftware[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
				isSoftwareFound = true
			}

			if len(rctxSoftware.Desc) == 0 {
				rctx.ResourceSoftwareTypes.DefinedSoftware[i].Desc = context.GetStaticDescriptionOfSoftwareType(rctxSoftware.Type)
			}
		}

		for i, rctxSoftware := range rctx.ResourceSoftwareTypes.DerivedSoftware {
			if strings.ToLower(rctxSoftware.Name) == strings.ToLower(software) && !isSoftwareFound {
				rctx.ResourceSoftwareTypes.DerivedSoftware[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
				isSoftwareFound = true
			}

			if len(rctxSoftware.Desc) == 0 {
				rctx.ResourceSoftwareTypes.DerivedSoftware[i].Desc = context.GetStaticDescriptionOfSoftwareType(rctxSoftware.Type)
			}
		}
	}

	for _, deployment := range primaryContext.Deployment {

		isDeploymentFound := false
		for i, rctxDeployment := range rctx.ResourceDeploymentTypes.DefinedDeployment {
			if strings.ToLower(rctxDeployment.Name) == strings.ToLower(deployment) && !isDeploymentFound {
				rctx.ResourceDeploymentTypes.DefinedDeployment[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
				isDeploymentFound = true
			}

			if len(rctxDeployment.Desc) == 0 {
				rctx.ResourceDeploymentTypes.DefinedDeployment[i].Desc = context.GetStaticDescriptionOfDeploymentType(rctxDeployment.Type)
			}
		}

		for i, rctxDeployment := range rctx.ResourceDeploymentTypes.DerivedDeployment {
			if strings.ToLower(rctxDeployment.Name) == strings.ToLower(deployment) && !isDeploymentFound {
				rctx.ResourceDeploymentTypes.DerivedDeployment[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
				isDeploymentFound = true
			}

			if len(rctxDeployment.Desc) == 0 {
				rctx.ResourceDeploymentTypes.DerivedDeployment[i].Desc = context.GetStaticDescriptionOfDeploymentType(rctxDeployment.Type)
			}
		}
	}

	for _, sensitivity := range primaryContext.Sensitivity {

		isSensitivityFound := false
		for i, rctxSensitivity := range rctx.ResourceSensitivityTypes.DefinedSensitivity {
			if strings.ToLower(rctxSensitivity.Name) == strings.ToLower(sensitivity) && !isSensitivityFound {
				rctx.ResourceSensitivityTypes.DefinedSensitivity[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
				isSensitivityFound = true
			}

			if len(rctxSensitivity.Desc) == 0 {
				rctxSensitivity.Desc = context.GetStaticDescriptionOfSensitivityType(rctxSensitivity.Type)
			}
		}

		for i, rctxSensitivity := range rctx.ResourceSensitivityTypes.DerivedSensitivity {
			if strings.ToLower(rctxSensitivity.Name) == strings.ToLower(sensitivity) && !isSensitivityFound {
				rctx.ResourceSensitivityTypes.DerivedSensitivity[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
				isSensitivityFound = true
			}

			if len(rctxSensitivity.Desc) == 0 {
				rctxSensitivity.Desc = context.GetStaticDescriptionOfSensitivityType(rctxSensitivity.Type)
			}
		}

		for i, rctxSensitivity := range rctx.ResourceSensitivityTypes.InheritedSensitivity {
			if strings.ToLower(rctxSensitivity.Name) == strings.ToLower(sensitivity) && !isSensitivityFound {
				rctx.ResourceSensitivityTypes.InheritedSensitivity[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
				isSensitivityFound = true
			}

			if len(rctxSensitivity.Desc) == 0 {
				rctxSensitivity.Desc = context.GetStaticDescriptionOfSensitivityType(rctxSensitivity.Type)
			}
		}
	}

	for _, compliance := range primaryContext.Compliance {

		isComplianceFound := false
		for i, rctxCompliance := range rctx.ResourceComplianceTypes.DefinedCompliance {
			if strings.ToLower(rctxCompliance.Name) == strings.ToLower(compliance) && !isComplianceFound {
				rctx.ResourceComplianceTypes.DefinedCompliance[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
				isComplianceFound = true
			}

			if len(rctxCompliance.Desc) == 0 {
				rctxCompliance.Desc = context.GetStaticDescriptionOfComplianceType(rctxCompliance.Type)
			}
		}

		for i, rctxCompliance := range rctx.ResourceComplianceTypes.DerivedCompliance {
			if strings.ToLower(rctxCompliance.Name) == strings.ToLower(compliance) && !isComplianceFound {
				rctx.ResourceComplianceTypes.DerivedCompliance[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
				isComplianceFound = true
			}

			if len(rctxCompliance.Desc) == 0 {
				rctxCompliance.Desc = context.GetStaticDescriptionOfComplianceType(rctxCompliance.Type)
			}
		}

		for i, rctxCompliance := range rctx.ResourceComplianceTypes.InheritedCompliance {
			if strings.ToLower(rctxCompliance.Name) == strings.ToLower(compliance) && !isComplianceFound {
				rctx.ResourceComplianceTypes.InheritedCompliance[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
				isComplianceFound = true
			}

			if len(rctxCompliance.Desc) == 0 {
				rctxCompliance.Desc = context.GetStaticDescriptionOfComplianceType(rctxCompliance.Type)
			}
		}
	}

	for _, costCenter := range primaryContext.CostCenter {

		isCostCenterFound := false
		for i, rctxCostCenter := range rctx.ResourceCostCenterTypes.DefinedCostCenter {
			if strings.ToLower(rctxCostCenter.Name) == strings.ToLower(costCenter) && !isCostCenterFound {
				rctx.ResourceCostCenterTypes.DefinedCostCenter[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
				isCostCenterFound = true
			}

			if len(rctxCostCenter.Desc) == 0 {
				rctx.ResourceCostCenterTypes.DefinedCostCenter[i].Desc = context.GetStaticDescriptionOfCostCenterType(rctxCostCenter.Type)
			}
		}

		for i, rctxCostCenter := range rctx.ResourceCostCenterTypes.InheritedCostCenter {
			if strings.ToLower(rctxCostCenter.Name) == strings.ToLower(costCenter) && !isCostCenterFound {
				rctx.ResourceCostCenterTypes.InheritedCostCenter[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
				isCostCenterFound = true
			}

			if len(rctxCostCenter.Desc) == 0 {
				rctxCostCenter.Desc = context.GetStaticDescriptionOfCostCenterType(rctxCostCenter.Type)
			}
		}
	}

	for _, ttl := range primaryContext.TTL {

		isTTLFound := false
		for i, rctxTTL := range rctx.ResourceTTLTypes.DefinedTTL {
			if strings.ToLower(rctxTTL.Name) == strings.ToLower(ttl) && !isTTLFound {
				rctx.ResourceTTLTypes.DefinedTTL[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
				isTTLFound = true
			}

			if len(rctxTTL.Desc) == 0 {
				rctx.ResourceTTLTypes.DefinedTTL[i].Desc = context.GetStaticDescriptionOfTTLType(rctxTTL.Type)
			}
		}

		for i, rctxTTL := range rctx.ResourceTTLTypes.DerivedTTL {
			if strings.ToLower(rctxTTL.Name) == strings.ToLower(ttl) && !isTTLFound {
				rctx.ResourceTTLTypes.DerivedTTL[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
				isTTLFound = true
			}

			if len(rctxTTL.Desc) == 0 {
				rctx.ResourceTTLTypes.DerivedTTL[i].Desc = context.GetStaticDescriptionOfTTLType(rctxTTL.Type)
			}
		}

		for i, rctxTTL := range rctx.ResourceTTLTypes.InheritedTTL {
			if strings.ToLower(rctxTTL.Name) == strings.ToLower(ttl) && !isTTLFound {
				rctx.ResourceTTLTypes.InheritedTTL[i].ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
				isTTLFound = true
			}

			if len(rctxTTL.Desc) == 0 {
				rctx.ResourceTTLTypes.InheritedTTL[i].Desc = context.GetStaticDescriptionOfTTLType(rctxTTL.Type)
			}
		}
	}

	return

}
