package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"

	"github.com/precize/common/weaviate"
	"github.com/weaviate/weaviate-go-client/v5/weaviate"
)

func main() {
	// Example 1: Using the common package helper function
	fmt.Println("=== Using Common Package Helper ===")
	resp, err := weaviate.GetClusterMetadata()
	if err != nil {
		log.Printf("Error getting cluster metadata: %v", err)
	} else {
		fmt.Printf("Cluster metadata response: %s\n", string(resp))
	}

	// Example 2: Direct API call using HTTP client (if you want to call the endpoint directly)
	fmt.Println("\n=== Direct Weaviate Client Usage ===")
	cfg := weaviate.Config{
		Host:   "localhost:8080",
		Scheme: "http",
	}
	client, err := weaviate.NewClient(cfg)
	if err != nil {
		log.Printf("Error creating Weaviate client: %v", err)
		return
	}

	meta, err := client.Misc().MetaGetter().Do(context.Background())
	if err != nil {
		log.Printf("Error getting cluster metadata: %v", err)
		return
	}

	// Pretty print the metadata
	metaJSON, err := json.MarshalIndent(meta, "", "  ")
	if err != nil {
		log.Printf("Error marshaling metadata: %v", err)
		return
	}

	fmt.Printf("Cluster metadata:\n%s\n", string(metaJSON))
}
